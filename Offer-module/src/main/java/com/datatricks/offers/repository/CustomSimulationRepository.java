package com.datatricks.offers.repository;

import com.datatricks.common.model.Simulation;
import com.datatricks.offers.model.dto.RelatedSimulationInfo;
import com.datatricks.offers.model.dto.SimulationDetailDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface CustomSimulationRepository extends JpaRepository<Simulation, Long>, JpaSpecificationExecutor<Simulation> {

	@Query(value = """
    SELECT 
		s.id AS id,
        s.title AS title,
        s.status AS status,
        p.code AS product_code,
        b.reference AS supplier_code,
        b.reference AS business_reference,
        a.code AS activity_code,
        se.feed_channel AS feed_channel,
        sa.actor_reference AS client,
        s.created_at AS created_at,
        se.start_date AS start_date,
        s.total_price_ttc AS price
    FROM
        dt_simulations s
    LEFT JOIN
        dt_simulation_element se ON s.id = se.simulation_id
    LEFT JOIN
        dt_products p ON s.product_id = p.id
    LEFT JOIN
        dt_offers o ON s.offer_reference = o.reference
    LEFT JOIN
        dt_actors b ON s.business_reference = b.reference
    LEFT JOIN
        dt_activities a ON s.activity_id = a.id
    LEFT JOIN
        dt_simulation_actors sa ON s.id = sa.simulation_reference AND sa.role_code = 'CLIENT'
    WHERE
        (:title IS NULL OR s.title LIKE %:title%)
        AND s.deleted_at IS NULL
	ORDER BY s.created_at DESC
	    
			""",
			countQuery = """
			SELECT COUNT(*) FROM
			    dt_simulations s
			LEFT JOIN
			    dt_simulation_element se ON s.id = se.simulation_id
			LEFT JOIN
			    dt_products p ON s.product_id = p.id
			LEFT JOIN
			    dt_offers o ON s.offer_reference = o.reference
			LEFT JOIN
			    dt_actors b ON s.business_reference = b.reference
			LEFT JOIN
			    dt_activities a ON s.activity_id = a.id
			LEFT JOIN
        		dt_simulation_actors sa ON s.id = sa.simulation_reference AND sa.role_code = 'CLIENT'
			WHERE ((:title IS NULL OR s.title LIKE %:title%) AND s.deleted_at IS NULL)
			""",
			nativeQuery = true)
	Page<Object[]> findSimulationsWithDetails(@Param("title") String title,
											  Pageable pageable);

	@Query("SELECT NEW com.datatricks.offers.model.dto.RelatedSimulationInfo(s.id, s.title, s.status,s.reference) FROM Simulation s WHERE s.offer.id = :offerId AND s.deletedAt IS NULL")
	List<RelatedSimulationInfo> findTitlesByOfferIdAndDeletedAtIsNull(@Param("offerId") Long offerId);
}
