package com.datatricks.offers.repository;

import com.datatricks.common.enums.TypeUnitePeriode;
import com.datatricks.common.model.*;
import com.datatricks.offers.model.dto.ScaleSearchDto;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.criteria.*;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public class CustomScaleRepositoryIml implements CustomScaleRepository {
    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<Scale> findByFilters(ScaleSearchDto dto) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Scale> query = cb.createQuery(Scale.class);
        Root<Scale> root = query.from(Scale.class);

        List<Predicate> predicates = new ArrayList<>();

        if (dto.getStartDate() != null) {
            Predicate nullEndDateCondition = cb.and(
                    cb.isNull(root.get("endDate")),
                    cb.greaterThanOrEqualTo(cb.literal(dto.getStartDate()), root.get("startDate"))
            );
            Predicate notNullEndDateCondition = cb.and(
                    cb.isNotNull(root.get("endDate")),
                    cb.between(cb.literal(dto.getStartDate()), root.get("startDate"), root.get("endDate"))
            );
            predicates.add(cb.or(nullEndDateCondition, notNullEndDateCondition));
        }
        if (dto.getProductCode() != null && !dto.getProductCode().isEmpty()) {
            // Join Scale with ScaleFinancialProduct and then with Product
            Join<Scale, ScaleFinancialProduct> financialProductJoin =
                    root.join("scaleFinancialProducts", JoinType.LEFT);
            Join<ScaleFinancialProduct, Product> productJoin =
                    financialProductJoin.join("product", JoinType.LEFT);

            // Check if the product code matches
            predicates.add(cb.equal(productJoin.get("code"), dto.getProductCode()));
        }
        //TODO: Do not filter by currency code until the user can set it in the Offre UI
//        if (dto.getCurrencyCode() != null && !dto.getCurrencyCode().isEmpty()) {
//            Join<Scale, Currency> activityJoin = root.join("currency", JoinType.LEFT);
//            predicates.add(cb.equal(activityJoin.get("code"), dto.getCurrencyCode()));
//        }
        if (dto.getNatureCode() != null && !dto.getNatureCode().isEmpty()) {
            Join<Scale, StaticParameter> activityJoin = root.join("nature", JoinType.LEFT);
            predicates.add(cb.equal(activityJoin.get("code"), dto.getNatureCode()));
        }

        if (dto.getChannelOfAcquisition() != null) {
            Join<Scale, ApplicationCriteria> applicationCriteriaJoin =
                    root.join("applicationCriteria", JoinType.LEFT);
            predicates.add(cb.equal(applicationCriteriaJoin.get("channelOfAcquisition"),
                    dto.getChannelOfAcquisition()));
        }

        if (dto.getPersonalContribution() != null && dto.getPersonalContribution() > 0) {
            predicates.add(cb.isTrue(root.get("hasPersonalContribution")));
            predicates.add(cb.between(
                    cb.literal(dto.getPersonalContribution()),
                    root.get("minimumPersonalContribution"),
                    root.get("maximumPersonalContribution")));
        }

        if (dto.getRatePeriod() != null ) {
            predicates.add(cb.equal(root.get("ratePeriod"), dto.getRatePeriod()));
        }

        if (dto.getAssetUsage() != null) {
            predicates.add(cb.equal(root.get("assetUsage"), dto.getAssetUsage()));
        }

        if (dto.getFinancingPeriod() != null) {
            predicates.add(cb.between(
                    cb.literal(dto.getFinancingPeriod()),
                    root.get("minimumFinancingPeriod"),
                    root.get("maximumFinancingPeriod")));
        }

        query.where(predicates.toArray(new Predicate[0]));

        return entityManager.createQuery(query).getResultList();
    }
}
