package com.datatricks.offers.model.dto;

import com.datatricks.common.enums.Acquisition;
import com.datatricks.common.enums.TypeUnitePeriode;
import com.datatricks.common.model.UsageTypes;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.time.LocalDate;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScaleSearchDto {

    @JsonProperty("start_date")
    private LocalDate startDate;

    @JsonProperty("product_code")
    private String productCode;

    @JsonProperty("currency_code")
    private String currencyCode;

    @JsonProperty("nature_code")
    private String natureCode;

    @JsonProperty("personal_contribution")
    private Double personalContribution;

    @JsonProperty("rate_period")
    private TypeUnitePeriode ratePeriod;

    @JsonProperty("asset_usage")
    private UsageTypes assetUsage;

    @JsonProperty("financing_period")
    private Integer financingPeriod;

    @JsonProperty("channel_of_acquisition")
    private Acquisition channelOfAcquisition;
}
