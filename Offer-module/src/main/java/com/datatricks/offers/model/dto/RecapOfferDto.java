package com.datatricks.offers.model.dto;

import com.datatricks.common.model.dto.MilestoneDto;
import com.datatricks.common.model.dto.PhaseDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class RecapOfferDto {
    private Long id;
    private String reference;

    @JsonProperty("phase")
    private PhaseDto phaseCode;

    @JsonProperty("milestone")
    private MilestoneDto milestoneCode;

    @JsonProperty("associated_simulations")
    private List<RelatedSimulationInfo> relatedSimulationInfoList;
}