package com.datatricks.offers.service;

import com.datatricks.common.enums.TypeBase;
import com.datatricks.common.enums.TypeTerme;
import com.datatricks.common.enums.TypeUnitePeriode;
import com.datatricks.common.model.Scale;
import com.datatricks.common.model.ScaleElementVehicle;
import com.datatricks.common.model.dto.*;
import com.datatricks.common.utils.DateUtils;
import com.datatricks.offers.model.dto.InitialFinanceDto;
import com.datatricks.offers.model.dto.external.EngineRubriqueFrontDto;
import com.datatricks.offers.model.dto.external.EngineTimetableLevelFrontDto;
import com.datatricks.common.repository.ScaleRepository;
import com.datatricks.offers.utils.OfferUtils;
import jakarta.transaction.Transactional;
import org.modelmapper.ModelMapper;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.beans.factory.annotation.Value;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;

@Service
public class SimulationFinanceService {

	@Value("${spring.services.engine.url}")
    private String ENGINE_SERVER_URL;

	private final RestTemplate restTemplate;
	private final ScaleRepository scaleRepository;
	private final ModelMapper modelMapper;

	public SimulationFinanceService(RestTemplate restTemplate,
									ScaleRepository scaleRepository,
									ModelMapper modelMapper) {
		this.restTemplate = restTemplate;
		this.scaleRepository = scaleRepository;
		this.modelMapper = modelMapper;
	}

	@Transactional
	public ResponseEntity<SingleResultDto<PageableDto>> createInitialFinance(InitialFinanceDto initialFinanceDto) {
		Scale scale = scaleRepository.findByReferenceAndDeletedAtIsNull(initialFinanceDto.getScaleReference())
				.orElse(null);
		EngineRubriqueFrontDto rubriqueFrontDto = createRubrique(initialFinanceDto);
		rubriqueFrontDto.setRate(scale == null ? 6.0 : scale.getNominalInterestRate());
		rubriqueFrontDto.setResidualValue(scale == null ? 0 : scale.getMinimumResidualValue());
		int missingLevelAmount = rubriqueFrontDto.getTimetableLevelFrontDtoList().size() - 1;
		ResponseEntity<SingleResultDto<EngineRubriqueFrontDto>> response = restTemplate.exchange(
				ENGINE_SERVER_URL + "finance-calculator/calculate-flow-amount/" + missingLevelAmount,
				HttpMethod.POST,
				new HttpEntity<>(rubriqueFrontDto), new ParameterizedTypeReference<>() {
				}
		);

        assert response.getBody() != null;
        RentalResponseDto mappedResponse = mapToRental(response.getBody().getData());
		mappedResponse.setPeriodNumber(initialFinanceDto.getPeriodNumber());
		mappedResponse.setPeriodicity(initialFinanceDto.getPeriodicity());
		mappedResponse.setInitialAmount(initialFinanceDto.getInitialAmount());
		mappedResponse.setMileage(scale instanceof ScaleElementVehicle ?
				((ScaleElementVehicle) scale).getMinimumMileage() : null);
		return ResponseEntity.ok(SingleResultDto.builder().data(mappedResponse).build());
	}

	private Date calculateEndDate(Date date, TypeUnitePeriode periodicity, int periodNumber) {
		LocalDate startDate = OfferUtils.convertToLocalDate(date);
		LocalDate responseDate = null;
		switch (periodicity) {
			case ENUM_ANNEE -> {
				responseDate = startDate.plusYears(periodNumber);
			}
			case ENUM_SEMESTRE -> {
				responseDate = startDate.plusMonths(
						6L * periodNumber);
			}
			case ENUM_TRIMESTRE -> {
				responseDate = startDate.plusMonths(3L * periodNumber);
			}
			case ENUM_MOIS -> { responseDate = startDate.plusMonths(periodNumber); }
			case ENUM_JOUR -> { responseDate = startDate.plusDays(periodNumber); }
		}
		return OfferUtils.convertToDate(responseDate);
	}

	private RentalResponseDto mapToRental(EngineRubriqueFrontDto data) {

		RentalResponseDto response = RentalResponseDto.builder().startDate(DateUtils.convertToLocalDate(data.getStartDate()))
				.originalAmount(data.getFinancialAmount())
				.endDate(DateUtils.convertToLocalDate(data.getEndDate()))
				.nominalRate(data.getRate())
				.equivalentRate(data.getEquivalentRate())
				.calculationMode(data.getDecompte())
				.calculationBasis(data.getBase())
				.taxRate(data.getTaxValue())
				.residualValue(data.getResidualValue())
				.rentalLevelsList(new ArrayList<>())
				.build();
		for (int order = 0; order < data.getTimetableLevelFrontDtoList().size(); order++) {
			LevelDto levelDto = new LevelDto();
			levelDto.setStartDate(data.getTimetableLevelFrontDtoList().get(order).getStartDate());
			levelDto.setPeriod(data.getTimetableLevelFrontDtoList().get(order).getTypeUnitePeriode());
			levelDto.setPeriodMultiple(data.getTimetableLevelFrontDtoList().get(order).getMultiple());
			levelDto.setPeriodNumber(data.getTimetableLevelFrontDtoList().get(order).getDuration());
			levelDto.setEndDate(calculateEndDate(levelDto.getStartDate(), levelDto.getPeriod(), levelDto.getPeriodNumber()));
			levelDto.setRent(data.getTimetableLevelFrontDtoList().get(order).getAmount());
			levelDto.setPerception(TypeTerme.ENUM_AVANCE);
			levelDto.setOrder(order + 1);
			// The rental end date is the last level end date
			response.setEndDate(DateUtils.convertToLocalDate(levelDto.getEndDate()));
			response.getRentalLevelsList().add(levelDto);
		}
		return response;
	}

	private EngineRubriqueFrontDto createRubrique(InitialFinanceDto initialFinanceDto) {
		EngineRubriqueFrontDto rubrique = new EngineRubriqueFrontDto();
		rubrique.setStartDate(initialFinanceDto.getStartDate());
		rubrique.setEndDate(initialFinanceDto.getStartDate());
		rubrique.setBase(TypeBase.ENUM_BASE360);
		rubrique.setDecompte(TypeBase.ENUM_BASE360);
		rubrique.setMultiple(1);
		rubrique.setRatePeriod(TypeUnitePeriode.ENUM_MOIS);
		//rubrique.setTaxValue(scale.getTax());
		rubrique.setFinancialAmount(initialFinanceDto.getFinancedAmount());
		if (initialFinanceDto.getInitialAmount() != null && initialFinanceDto.getInitialAmount() != 0) {
			EngineTimetableLevelFrontDto level = new EngineTimetableLevelFrontDto();
			level.setOrder(1);
			level.setStartDate(initialFinanceDto.getStartDate());
			level.setTypeUnitePeriode(TypeUnitePeriode.ENUM_MOIS);
			level.setMultiple(1);
			level.setFrequency(1);
			level.setDuration(1);
			level.setAmount(initialFinanceDto.getInitialAmount());
			rubrique.getTimetableLevelFrontDtoList().add(level);
		}
		EngineTimetableLevelFrontDto level = new EngineTimetableLevelFrontDto();
		level.setOrder(rubrique.getTimetableLevelFrontDtoList().size() + 1);
		if (initialFinanceDto.getInitialAmount() != null) {
			level.setStartDate(OfferUtils.convertToDate(OfferUtils.convertToLocalDate(initialFinanceDto.getStartDate()).plusMonths(1)));
		} else {
			level.setStartDate(OfferUtils.convertToDate(OfferUtils.convertToLocalDate(initialFinanceDto.getStartDate()).plusMonths(1)));
		}
		level.setTypeUnitePeriode(initialFinanceDto.getPeriodicity());
		level.setMultiple(1);
		level.setFrequency(1);
		level.setDuration(initialFinanceDto.getPeriodNumber());
		level.setAmount(initialFinanceDto.getInitialAmount());
		rubrique.getTimetableLevelFrontDtoList().add(level);
		return rubrique;
	}

}
