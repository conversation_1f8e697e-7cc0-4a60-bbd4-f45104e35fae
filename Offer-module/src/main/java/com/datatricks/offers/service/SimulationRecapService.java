package com.datatricks.offers.service;

import com.datatricks.common.model.*;
import com.datatricks.common.model.dto.*;
import com.datatricks.common.repository.*;
import com.datatricks.common.utils.JpaQueryFilters;
import com.datatricks.offers.exception.ResourcesNotFoundException;
import com.datatricks.offers.model.dto.RecapSimulationActorDto;
import com.datatricks.offers.model.dto.RecapSimulationDto;
import com.datatricks.offers.model.dto.RecapSimulationElementDto;
import com.datatricks.offers.model.dto.RelatedSimulationInfo;
import com.datatricks.offers.repository.CustomSimulationRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class SimulationRecapService {

	private static final String MODULE = "Offer";
	private final VehicleRepository vehicleRepository;
	private final SimulationRepository simulationRepository;
	private final SimulationElementRepository simulationElementRepository;
	private final MaterialRepository materialRepository;
	private final ModelMapper modelMapper;
	private final SimulationActorAddressRepository simulationActorAddressRepository;
	private final SimulationActorPaymentsRepository simulationActorPaymentsRepository;
	private final FileSystemService fileSystemService;
	private final CustomSimulationRepository customSimulationRepository;



	@Transactional
	public ResponseEntity<SingleResultDto<RecapSimulationDto>> getSimulationsRecap(Long simulationId) {
		Simulation simulation = simulationRepository.findByIdAndDeletedAtIsNull(simulationId)
				.orElseThrow(() -> new ResourcesNotFoundException("Simulation not found", MODULE));

		RecapSimulationDto dto = modelMapper.map(simulation, RecapSimulationDto.class);

		List<RelatedSimulationInfo> simulationsTitle =
				customSimulationRepository.findTitlesByOfferIdAndDeletedAtIsNull(simulation.getOffer().getId());

		dto.getOffer().setRelatedSimulationInfoList(simulationsTitle);
		dto.setSimulationActor(null);

		List<RecapSimulationElementDto> elementDtos = simulationElementRepository
				.findAllBySimulationIdAndDeletedAtIsNull(simulation.getId())
				.stream()
				.map(se -> {
					RecapSimulationElementDto elementDto = modelMapper.map(se, RecapSimulationElementDto.class);
					Long id = se.getElement().getId();
					String natureCode = se.getElement().getNature().getCode();

					if ("VH".equals(natureCode)) {
						vehicleRepository.findByIdAndDeletedAtIsNull(id)
								.map(v -> modelMapper.map(v, ElementDto.class))
								.ifPresent(elementDto::setElement);
					} else {
						materialRepository.findByIdAndDeletedAtIsNull(id)
								.map(m -> modelMapper.map(m, ElementDto.class))
								.ifPresent(elementDto::setElement);
					}

					return elementDto;
				})
				.collect(Collectors.toList());
		dto.setSimulationElements(elementDtos);

		Set<RecapSimulationActorDto> simulationActors = simulation.getSimulationActor()
				.stream()
				.filter(simulationActor -> simulationActor.getDeletedAt() == null)
				.map(simulationActor -> {
					RecapSimulationActorDto actorDto = modelMapper.map(simulationActor, RecapSimulationActorDto.class);

					// Fetch and map SimulationActorAddress
					simulationActorAddressRepository.findBySimulationActorIdIdAndDeletedAtIsNull(simulationActor.getId())
							.map(address -> modelMapper.map(address, SimulationActorAddressDto.class))
							.ifPresent(actorDto::setAddresses);

					// Fetch and map SimulationActorAddress
					List<SimulationActorPaymentsDto> payments = simulationActorPaymentsRepository
							.findBySimulationActorIdAndDeletedAtIsNull(simulationActor.getId())
							.stream()
							.map(payment -> modelMapper.map(payment, SimulationActorPaymentsDto.class))
							.collect(Collectors.toList());
					actorDto.setPayments(payments);

					if (actorDto.getRentals() != null) {
						actorDto.getRentals().forEach(rental -> {
							if (rental.getStartDate() != null && rental.getEndDate() != null) {
								int duration = calculateDurationInMonths(rental.getStartDate(), rental.getEndDate());
								rental.setDuration(duration);
							} else {
								rental.setDuration(0);
							}
						});
					}

					actorDto.setPid(fetchFileResult(simulationActor.getPid()));
					actorDto.setExtraitKbis(fetchFileResult(simulationActor.getExtraitKbis()));
					actorDto.setBilan(fetchFileResult(simulationActor.getBilan()));
					actorDto.setCni(fetchFileResult(simulationActor.getCni()));
					actorDto.setRib(fetchFileResult(simulationActor.getRib()));
					actorDto.setMandatSepa(fetchFileResult(simulationActor.getMandatSepa()));

					return actorDto;
				})
				.collect(Collectors.toSet());
		dto.setSimulationActor(simulationActors);

		SingleResultDto<RecapSimulationDto> resultDto = SingleResultDto.<RecapSimulationDto>builder()
				.data(dto)
				.build();

		return ResponseEntity.ok(resultDto);
	}



	@Transactional
	public ResponseEntity<PageDto<RecapSimulationDto>> getAllSimulationsRecap(Map<String, String> params) {
		// Initialize pagination
		JpaQueryFilters<Simulation> filters = new JpaQueryFilters<>(params, Simulation.class);
		Page<Simulation> simulationPage = simulationRepository.findAll(filters.getSpecification(), filters.getPageable());

		List<RecapSimulationDto> recapSimulationDtos = simulationPage.getContent().stream()
				.map(simulation -> {
					RecapSimulationDto dto = modelMapper.map(simulation, RecapSimulationDto.class);
					List<RelatedSimulationInfo> simulationsTitle =
							customSimulationRepository.findTitlesByOfferIdAndDeletedAtIsNull(simulation.getOffer().getId());

					dto.getOffer().setRelatedSimulationInfoList(simulationsTitle);
					List<RecapSimulationElementDto> elementDtos = simulationElementRepository
							.findAllBySimulationIdAndDeletedAtIsNull(simulation.getId())
							.stream()
							.map(se -> {
								RecapSimulationElementDto elementDto = modelMapper.map(se, RecapSimulationElementDto.class);
								Long id = se.getElement().getId();
								String natureCode = se.getElement().getNature().getCode();

								// Map element based on nature code
								if ("VH".equals(natureCode)) {
									vehicleRepository.findByIdAndDeletedAtIsNull(id)
											.map(v -> modelMapper.map(v, ElementDto.class))
											.ifPresent(elementDto::setElement);
								} else {
									materialRepository.findByIdAndDeletedAtIsNull(id)
											.map(m -> modelMapper.map(m, ElementDto.class))
											.ifPresent(elementDto::setElement);
								}

								return elementDto;
							})
							.collect(Collectors.toList());

					dto.setSimulationElements(elementDtos);

					Set<RecapSimulationActorDto> simulationActors = simulation.getSimulationActor()
							.stream()
							.filter(simulationActor -> simulationActor.getDeletedAt() == null)
							.map(simulationActor -> {
								RecapSimulationActorDto actorDto = modelMapper.map(simulationActor, RecapSimulationActorDto.class);

								// Fetch and map SimulationActorAddresses
								simulationActorAddressRepository.findBySimulationActorIdIdAndDeletedAtIsNull(simulationActor.getId())
										.map(address -> modelMapper.map(address, SimulationActorAddressDto.class))
										.ifPresent(actorDto::setAddresses);

								// Fetch and map SimulationActorPayments
								List<SimulationActorPaymentsDto> payments = simulationActorPaymentsRepository
										.findBySimulationActorIdAndDeletedAtIsNull(simulationActor.getId())
										.stream()
										.map(payment -> modelMapper.map(payment, SimulationActorPaymentsDto.class))
										.collect(Collectors.toList());
								actorDto.setPayments(payments);

								if (actorDto.getRentals() != null) {
									actorDto.getRentals().forEach(rental -> {
										if (rental.getStartDate() != null && rental.getEndDate() != null) {
											int duration = calculateDurationInMonths(rental.getStartDate(), rental.getEndDate());
											rental.setDuration(duration);
										} else {
											rental.setDuration(0);
										}
									});
								}

								return actorDto;
							})
							.collect(Collectors.toSet());
					dto.setSimulationActor(simulationActors);

					return dto;
				})
				.collect(Collectors.toList());

		PageDto<RecapSimulationDto> pageDto = PageDto.<RecapSimulationDto>builder()
				.total(simulationPage.getTotalElements())
				.data(recapSimulationDtos)
				.build();

		return ResponseEntity.ok(pageDto);
	}


	@Transactional
	public ResponseEntity<PageDto<FileResultDto>> getSimulationFiles(Long simulationId) {
		Simulation simulation = simulationRepository.findByIdAndDeletedAtIsNull(simulationId)
				.orElseThrow(() -> new ResourcesNotFoundException("Simulation not found", MODULE));

		List<FileResultDto> files = simulation.getSimulationActor()
				.stream()
				.filter(simulationActor -> simulationActor.getDeletedAt() == null)
				.flatMap(simulationActor -> Stream.of(
						fetchFileResult(simulationActor.getPid()),
						fetchFileResult(simulationActor.getExtraitKbis()),
						fetchFileResult(simulationActor.getBilan()),
						fetchFileResult(simulationActor.getCni()),
						fetchFileResult(simulationActor.getRib()),
						fetchFileResult(simulationActor.getMandatSepa())
				))
				.filter(Objects::nonNull)
				.collect(Collectors.toList());

		PageDto<FileResultDto> pageDto = PageDto.<FileResultDto>builder()
				.data(files)
				.total(files.size())
				.build();

		return ResponseEntity.ok(pageDto);
	}



	private int calculateDurationInMonths(Date startDate, Date endDate) {
		Calendar startCalendar = Calendar.getInstance();
		startCalendar.setTime(startDate);

		Calendar endCalendar = Calendar.getInstance();
		endCalendar.setTime(endDate);

		int yearsDifference = endCalendar.get(Calendar.YEAR) - startCalendar.get(Calendar.YEAR);
		int monthsDifference = endCalendar.get(Calendar.MONTH) - startCalendar.get(Calendar.MONTH);

		int duration = yearsDifference * 12 + monthsDifference;

		if (duration < 0) {
			throw new ResourcesNotFoundException("End date must be after start date. Calculated duration is negative.", MODULE);
		}

		return duration;
	}

	private FileResultDto fetchFileResult(String fileName) {
		if (fileName == null || fileName.isEmpty()) {
			return null;
		}

		try {
			return fileSystemService.getFile(fileName)
					.getBody()
					.getData();
		} catch (Exception e) {
			return null;
		}
	}

}


