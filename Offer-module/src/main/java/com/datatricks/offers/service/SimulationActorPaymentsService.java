package com.datatricks.offers.service;

import com.datatricks.offers.exception.ConflictException;
import com.datatricks.offers.exception.ResourcesNotFoundException;
import com.datatricks.offers.exception.handler.InformativeMessage;
import com.datatricks.common.model.*;
import com.datatricks.common.model.dto.SimulationActorPaymentsDto;
import com.datatricks.common.model.dto.SimulationActorPaymentsInput;
import com.datatricks.common.model.dto.PageDto;
import com.datatricks.common.model.dto.SingleResultDto;
import com.datatricks.common.repository.BankAccountRepository;
import com.datatricks.common.repository.SimulationActorPaymentsRepository;
import com.datatricks.common.repository.SimulationActorRepository;
import com.datatricks.common.repository.PaymentMethodRepository;
import jakarta.transaction.Transactional;
import org.modelmapper.ModelMapper;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
public class SimulationActorPaymentsService {

    private final SimulationActorPaymentsRepository simulationActorPaymentsRepository;
    private final SimulationActorRepository simulationActorRepository;
    private final ModelMapper modelMapper;
    private final BankAccountRepository bankAccountRepository;
    private static final String SIMULATION_ACTOR_PAYMENT_NOT_FOUND = "Simulation Actor Payment not found";
    private static final String SIMULATION_ACTOR_NOT_FOUND = "Simulation Actor not found";
    private static final String BANK_ACCOUNT_NOT_FOUND = "Bank Account not found";
    private static final String PAYMENT_METHOD_NOT_FOUND = "Payment method not found";
    private static final String SIMULATION_ACTOR_PAYMENT_ALREADY_EXISTS =
            "Simulation Actor Payment already exists for this simulation actor";
    private static final String MODULE = "Simulation Actor Payments";
    private final PaymentMethodRepository paymentMethodRepository;

    public SimulationActorPaymentsService(
            SimulationActorPaymentsRepository simulationActorPaymentsRepository,
            SimulationActorRepository simulationActorRepository,
            ModelMapper modelMapper,
            BankAccountRepository bankAccountRepository,
            PaymentMethodRepository paymentMethodRepository) {
        this.simulationActorPaymentsRepository = simulationActorPaymentsRepository;
        this.simulationActorRepository = simulationActorRepository;
        this.modelMapper = modelMapper;
        this.bankAccountRepository = bankAccountRepository;
        this.paymentMethodRepository = paymentMethodRepository;
    }

    @Transactional
    public ResponseEntity<SingleResultDto<SimulationActorPaymentsDto>> addSimulationActorPayments(
            Long simulationActorId, SimulationActorPaymentsInput simulationActorPaymentsInput) {

        SimulationActor simulationActor = simulationActorRepository.findByIdAndDeletedAtIsNull(simulationActorId).orElseThrow(
                () -> new ResourcesNotFoundException(SIMULATION_ACTOR_NOT_FOUND, MODULE));

        SimulationActorPayments simulationActorPayments = new SimulationActorPayments();
        simulationActorPayments.setSimulationActor(simulationActor);
        PaymentMethod paymentMethod = paymentMethodRepository.findByCode(
                simulationActorPaymentsInput.getPaymentMethodCode()).orElseThrow(
                        () -> new ResourcesNotFoundException(PAYMENT_METHOD_NOT_FOUND, MODULE));

        simulationActorPayments.setPaymentMethod(paymentMethod);
        if(paymentMethod.getRequiresBankAccount()) {
            var bankAccountId = simulationActorPaymentsInput.getBankAccountId();
            if (bankAccountId == null || bankAccountId == 0) {
                throw new ResourcesNotFoundException(BANK_ACCOUNT_NOT_FOUND, MODULE);
            }
            BankAccount bankAccount = bankAccountRepository.findByActorIdIdAndIdAndDeletedAtIsNull(
                    simulationActorPaymentsInput.getType() != PaymentsType.Target ?
                            simulationActor.getActor().getId() :
                            simulationActor.getSimulation().getBusinessReference().getId(),
                    bankAccountId);

            if (bankAccount == null) {
                throw new ResourcesNotFoundException(BANK_ACCOUNT_NOT_FOUND, MODULE);
            }
            simulationActorPayments.setBankAccount(bankAccount);
        }
        Optional<SimulationActorPayments> duplicatePayment = simulationActorPaymentsRepository
                .findBySimulationActorIdAndTypeAndDeletedAtIsNull(simulationActorId, simulationActorPaymentsInput.getType());
        if (duplicatePayment.isPresent()) {
            throw new ConflictException(
                    SIMULATION_ACTOR_PAYMENT_ALREADY_EXISTS,
                    "SIMULATION_ACTOR_PAYMENT_ALREADY_EXISTS",
                    SIMULATION_ACTOR_PAYMENT_ALREADY_EXISTS,
                    MODULE);
        }

        simulationActorPayments.setType(simulationActorPaymentsInput.getType());
        simulationActorPayments.setStartDate(simulationActorPaymentsInput.getStartDate());

        simulationActorPayments.setCreatedAt(new Date());
        simulationActorPaymentsRepository.save(simulationActorPayments);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(SingleResultDto.<SimulationActorPaymentsDto>builder()
                        .data(modelMapper.map(simulationActorPayments, SimulationActorPaymentsDto.class))
                        .build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<SimulationActorPaymentsDto>> updateSimulationActorPayments(
            Long simulationActorId, Long simulationActorPaymentId, SimulationActorPaymentsInput simulationActorPaymentsInput) {

        SimulationActor simulationActor = simulationActorRepository.findByIdAndDeletedAtIsNull(simulationActorId).orElseThrow(
                () -> new ResourcesNotFoundException(SIMULATION_ACTOR_NOT_FOUND, MODULE));

        SimulationActorPayments simulationActorPayments =
                simulationActorPaymentsRepository.findByIdAndDeletedAtIsNull(simulationActorPaymentId).orElseThrow(
                        () -> new ResourcesNotFoundException(SIMULATION_ACTOR_PAYMENT_NOT_FOUND, MODULE)
                );
        PaymentMethod paymentMethod = paymentMethodRepository.findByCode(
                simulationActorPaymentsInput.getPaymentMethodCode()).orElseThrow(
                () -> new ResourcesNotFoundException(PAYMENT_METHOD_NOT_FOUND, MODULE));

        simulationActorPayments.setPaymentMethod(paymentMethod);
        if(paymentMethod.getRequiresBankAccount()) {
            var bankAccountId = simulationActorPaymentsInput.getBankAccountId();
            if (bankAccountId == null || bankAccountId == 0) {
                throw new ResourcesNotFoundException(BANK_ACCOUNT_NOT_FOUND, MODULE);
            }
            BankAccount bankAccount = bankAccountRepository.findByActorIdIdAndIdAndDeletedAtIsNull(
                    simulationActorPaymentsInput.getType() != PaymentsType.Target ?
                            simulationActor.getActor().getId() :
                            simulationActor.getSimulation().getBusinessReference().getId(),
                    bankAccountId);

            if (bankAccount == null) {
                throw new ResourcesNotFoundException(BANK_ACCOUNT_NOT_FOUND, MODULE);
            }
            simulationActorPayments.setBankAccount(bankAccount);
        } else {
            simulationActorPayments.setBankAccount(null);
        }

        Optional<SimulationActorPayments> duplicatePayment = simulationActorPaymentsRepository
                .findBySimulationActorIdAndTypeAndDeletedAtIsNull(simulationActorId, simulationActorPaymentsInput.getType());
        if (duplicatePayment.isPresent() && !duplicatePayment.get().getId().equals(simulationActorPaymentId)) {
            throw new ConflictException(
                    SIMULATION_ACTOR_PAYMENT_ALREADY_EXISTS,
                    "SIMULATION_ACTOR_PAYMENT_ALREADY_EXISTS",
                    SIMULATION_ACTOR_PAYMENT_ALREADY_EXISTS,
                    MODULE);
        }

        simulationActorPayments.setType(simulationActorPaymentsInput.getType());
        simulationActorPayments.setStartDate(simulationActorPaymentsInput.getStartDate());

        simulationActorPayments.setModifiedAt(new Date());
        simulationActorPaymentsRepository.save(simulationActorPayments);

        return ResponseEntity.status(HttpStatus.OK)
                .body(SingleResultDto.<SimulationActorPaymentsDto>builder()
                        .data(modelMapper.map(simulationActorPayments, SimulationActorPaymentsDto.class))
                        .build());
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteSimulationActorPayments(Long simulationActorId, Long id) {
        simulationActorRepository.findByIdAndDeletedAtIsNull(simulationActorId).orElseThrow(
                () -> new ResourcesNotFoundException(SIMULATION_ACTOR_NOT_FOUND, MODULE)
        );
        SimulationActorPayments simulationActorPayments =
                simulationActorPaymentsRepository.findByIdAndDeletedAtIsNull(id).orElseThrow(
                        () -> new ResourcesNotFoundException(SIMULATION_ACTOR_PAYMENT_NOT_FOUND, MODULE)
                );
        simulationActorPayments.setDeletedAt(new Date());
        simulationActorPaymentsRepository.save(simulationActorPayments);
        return ResponseEntity.status(HttpStatus.OK)
                .body(new InformativeMessage("Resource with ID " + id + " has been deleted successfully"));
    }

    @Transactional
    public ResponseEntity<PageDto<SimulationActorPaymentsDto>> getSimulationActorPayments(Long simulationActorId) {
        List<SimulationActorPayments> simulationActorPayments =
                simulationActorPaymentsRepository.findBySimulationActorIdAndDeletedAtIsNull(simulationActorId);
        List<SimulationActorPaymentsDto> pageableDto = simulationActorPayments.stream()
                .map(simulationActorPayment -> modelMapper.map(simulationActorPayment, SimulationActorPaymentsDto.class))
                .toList();
        return ResponseEntity.ok(PageDto.<SimulationActorPaymentsDto>builder()
                .data(pageableDto)
                .total(simulationActorPayments.size())
                .build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<SimulationActorPaymentsDto>> getSimulationActorPaymentsById(Long simulationActorId, Long simulationActorPaymentsId) {
        simulationActorRepository.findByIdAndDeletedAtIsNull(simulationActorId).orElseThrow(
                () -> new ResourcesNotFoundException(SIMULATION_ACTOR_NOT_FOUND, MODULE)
        );
        SimulationActorPayments simulationActorPayments = simulationActorPaymentsRepository.findByIdAndDeletedAtIsNull(simulationActorPaymentsId)
                .orElseThrow(() -> new ResourcesNotFoundException(SIMULATION_ACTOR_PAYMENT_NOT_FOUND, MODULE));
        SimulationActorPaymentsDto response = modelMapper.map(simulationActorPayments, SimulationActorPaymentsDto.class);
        return new ResponseEntity<>(
                SingleResultDto.<SimulationActorPaymentsDto>builder()
                        .data(response)
                        .build(),
                HttpStatus.OK);
    }
}
