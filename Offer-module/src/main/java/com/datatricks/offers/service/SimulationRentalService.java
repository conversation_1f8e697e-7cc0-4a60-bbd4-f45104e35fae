package com.datatricks.offers.service;

import com.datatricks.common.enums.TypeUnitePeriode;
import com.datatricks.common.model.*;
import com.datatricks.common.model.dto.*;
import com.datatricks.common.repository.*;
import com.datatricks.offers.exception.BusinessException;
import com.datatricks.offers.exception.ConflictException;
import com.datatricks.offers.exception.ResourcesNotFoundException;
import com.datatricks.offers.exception.handler.InformativeMessage;
import com.datatricks.offers.model.dto.RecapSimulationElementDto;
import com.datatricks.offers.model.dto.external.EngineRateCalculation;
import com.datatricks.offers.model.dto.external.EngineRubriqueFrontDto;
import com.datatricks.offers.model.dto.external.EngineScheduleLine;
import com.datatricks.offers.model.dto.external.EngineTimetableLevelFrontDto;
import com.datatricks.offers.utils.OfferUtils;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;

@Service
@RequiredArgsConstructor
public class SimulationRentalService {

    private static final String FINANCE_STATUS = "draft_finance";
    private static final String MODULE = "RENTAL";
    private static final String RENTAL_NOT_FOUND = "Rental not found";
    private static final String VEHICLE_NATURE_CODE = "VH";
    private final LineTypeRepository lineTypeRepository;
    private final VehicleRepository vehicleRepository;
    private final SimulationRecapService simulationRecapService;
    @Value("${spring.services.engine.url}")
    private String ENGINE_SERVER_URL;

    private final RestTemplate restTemplate;
    private final ModelMapper modelMapper;
    private final TimetableRepository timetableRepository;
    private final RentalRepository rentalRepository;
    private final LevelRepository levelRepository;
    private final TimetableItemRepository timetableItemRepository;
    private final ScaleRepository scaleRepository;
    private final SimulationScaleService simulationScaleService;

    @Transactional
    public ResponseEntity<SingleResultDto<RentalResponseDto>> createRental(Long simulationId,
                                                                           SimulationActor simulationActor,
                                                                           RentalDto rentalDto) {

        if (!CollectionUtils.isEmpty(rentalDto.getRentalLevelsList()) && !isRentalSectionValid(rentalDto)) {
            throw new ConflictException(
                    "Invalid rental rates",
                    "RENTAL_INVALID_RATES",
                    "Invalid rental rates",
                    MODULE);
        }

        LineType lineType = lineTypeRepository.findByCode(rentalDto.getLineTypeCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Line type not found", MODULE));
        Scale scale = scaleRepository.findByReferenceAndDeletedAtIsNull(rentalDto.getScaleReference())
                .orElse(null);
        if (scale != null) {
            validateRentalAgainstScale(rentalDto, scale, simulationActor);
        }

        Timetable newTimetable = null;
        if (scale == null) {
            newTimetable = Timetable.builder()
                    .status("INITIALIZED")
                    .residualValue(rentalDto.getResidualValue()).build();
        } else {
            newTimetable = Timetable.builder()
                    .status("INITIALIZED")
                    .currencyCode(scale.getCurrency().getCode())
                    .residualValue(rentalDto.getResidualValue()).build();
        }
        Timetable timetable = timetableRepository.save(newTimetable);

        Rental rental = new Rental(rentalDto);
        rental.setId(null);
        rental.setTimetableId(timetable);
        rental.setSimulationActorId(simulationActor);
        rental.setLineType(lineType);
        rental.setScaleReference(scale);
        rental.setCreatedAt(new Date());
        Rental newRental = rentalRepository.save(rental);
        RentalDto newRentalDto = modelMapper.map(newRental, RentalDto.class);
        rentalDto.getRentalLevelsList().forEach(levelDto -> {
            Level level = modelMapper.map(levelDto, Level.class);
            level.setId(null);
            level.setTimetableId(timetable);
            level.setCreatedAt(new Date());
            Level newLevel = levelRepository.save(level);
            LevelDto newLevelDto = modelMapper.map(newLevel, LevelDto.class);
            newRentalDto.getRentalLevelsList().add(newLevelDto);
        });

        if (!CollectionUtils.isEmpty(newRentalDto.getRentalLevelsList())) {
            createSchedule(simulationId, newRentalDto);
        }
        RentalResponseDto rentalResponseDto = modelMapper.map(newRentalDto, RentalResponseDto.class);
        setMileage(simulationId, rentalResponseDto, rentalDto.getMileage());

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(SingleResultDto.<RentalResponseDto>builder()
                        .data(rentalResponseDto)
                        .build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<RentalResponseDto>> updateRental(Long simulationId,
                                                                           SimulationActor simulationActor,
                                                                           Long rentalId,
                                                                           RentalDto rentalDto) {

        if (!CollectionUtils.isEmpty(rentalDto.getRentalLevelsList()) && !isRentalSectionValid(rentalDto)) {
            throw new ConflictException(
                    "Invalid rental rates",
                    "RENTAL_INVALID_RATES",
                    "Invalid rental rates",
                    MODULE);
        }

        LineType lineType = lineTypeRepository.findByCode(rentalDto.getLineTypeCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Line type not found", MODULE));
        Scale scale = scaleRepository.findByReferenceAndDeletedAtIsNull(rentalDto.getScaleReference())
                .orElse(null);
        if (scale != null) {
            validateRentalAgainstScale(rentalDto, scale, simulationActor);
        }

        Rental rentalToUpdate =
				rentalRepository.findBySimulationActorIdSimulationIdAndIdAndDeletedAtIsNull(simulationId, rentalId);
        if (rentalToUpdate == null) {
            throw new ResourcesNotFoundException(RENTAL_NOT_FOUND, MODULE);
        }

		Date newDate = new Date();
		Rental rental = new Rental(rentalDto);
        rental.setId(rentalToUpdate.getId());
		rental.setSimulationActorId(simulationActor);
        rental.setTimetableId(rentalToUpdate.getTimetableId());
		rental.setLineType(lineType);
        rental.setScaleReference(scale);
		rental.setCreatedAt(rentalToUpdate.getCreatedAt());
		rental.setModifiedAt(newDate);

        rentalRepository.save(rental);

		levelRepository.deleteAllByTimetableIdIdAndDeletedAtIsNull(rental.getTimetableId().getId());
        List<Level> newLevels = new ArrayList<>();
		List<LevelDto> newSortedLevelsDto = rentalDto.getRentalLevelsList().stream()
				.sorted(Comparator.comparing(LevelDto::getStartDate))
				.toList();
		for (var i = 0; i < newSortedLevelsDto.size(); i++) {
			Level level = modelMapper.map(newSortedLevelsDto.get(i), Level.class);
			level.setId(null);
			level.setOrder(i + 1);
            level.setTimetableId(rental.getTimetableId());
			level.setCreatedAt(newDate);
			level.setModifiedAt(newDate);
			newLevels.add(level);
        }
        levelRepository.saveAll(newLevels);
        ResponseEntity<SingleResultDto<RentalResponseDto>> createdRental = getRental(simulationId, rentalId);
        if (!CollectionUtils.isEmpty(
                (Objects.requireNonNull(createdRental.getBody()).getData()).getRentalLevelsList())) {
            createSchedule(
                    simulationId,
                    modelMapper.map(
                            Objects.requireNonNull(createdRental.getBody())
                                    .getData(),
                            RentalDto.class));
        }
        setMileage(simulationId, createdRental.getBody().getData(), rentalDto.getMileage());
        return createdRental;
    }

    @Transactional
    public ResponseEntity<SingleResultDto<RentalResponseDto>> getRental(Long simulationId, Long rentalId) {
        Rental rental =
                rentalRepository
						.findBySimulationActorIdSimulationIdAndIdAndDeletedAtIsNull(simulationId, rentalId);
        if (rental == null) {
            throw new ResourcesNotFoundException(RENTAL_NOT_FOUND, MODULE);
        }

        RentalResponseDto rentalDto = modelMapper.map(rental, RentalResponseDto.class);
        rentalDto.setTimetableId(modelMapper.map(rental.getTimetableId(), TimetableDto.class));
        List<Level> levels = levelRepository.findByTimetableIdIdAndDeletedAtIsNull(
                rental.getTimetableId().getId());
        rentalDto.setRentalLevelsList(levels.stream()
                .map(level -> modelMapper.map(level, LevelDto.class))
                .sorted(Comparator.comparing(LevelDto::getStartDate))
                .toList());
        setMileage(simulationId, rentalDto, null);
        return ResponseEntity.ok(SingleResultDto.<RentalResponseDto>builder().data(rentalDto).build());
    }

    @Transactional
    public ResponseEntity<PageDto<RentalResponseDto>> getRentals(Long simulationId) {
		List<Rental> page = rentalRepository.findBySimulationActorIdSimulationIdAndDeletedAtIsNullOrderByCreatedAtDesc(simulationId);
        List<RentalResponseDto> rentals = page.stream()
                .map(element -> {
                    RentalResponseDto rentalDto = modelMapper.map(element, RentalResponseDto.class);
                    List<Level> levels = levelRepository
                            .findByTimetableIdIdAndDeletedAtIsNull(rentalDto.getTimetableId().getId());
                    setMileage(simulationId, rentalDto, null);
                    rentalDto.setRentalLevelsList(levels.stream()
                            .map(level -> modelMapper.map(level, LevelDto.class))
                            .sorted(Comparator.comparing(LevelDto::getStartDate))
                            .toList());
                    return rentalDto;
                })
                .toList();
        return ResponseEntity.ok(PageDto.<RentalResponseDto>builder()
                .data(rentals)
                .total(page.size()).build());
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteRental(Long id, Long rentalId) {

        Rental rental =
                rentalRepository
						.findBySimulationActorIdSimulationIdAndIdAndDeletedAtIsNull(id, rentalId);
        if (rental == null) {
            throw new ResourcesNotFoundException(RENTAL_NOT_FOUND, MODULE);
        }
        Date deletedDate = new Date();
        rental.setDeletedAt(deletedDate);
        rentalRepository.save(rental);
        List<Level> levels = levelRepository.findByTimetableIdIdAndDeletedAtIsNull(
                rental.getTimetableId().getId());
        levels.forEach(level -> level.setDeletedAt(deletedDate));
        levelRepository.saveAll(levels);
        rental.getTimetableId().setDeletedAt(deletedDate);
        timetableRepository.save(rental.getTimetableId());
        timetableItemRepository.deleteAllByTimetableIdId(rental.getTimetableId().getId());

        return ResponseEntity.ok(new InformativeMessage("Resource with ID " + rentalId + " has been deleted successfully"));
    }

    @Transactional
    public void createSchedule(Long id, RentalDto lastRentalDto) {
        List<EngineScheduleLine> generatedSchedule = generateSchedule(lastRentalDto);

        Long finalLastRentalDto = lastRentalDto.getTimetableId();
        timetableItemRepository.deleteAllByTimetableIdId(finalLastRentalDto);
        if (generatedSchedule != null) {
            List<TimetableItem> timetableItems = generatedSchedule.stream()
                    .map(engineScheduleLine -> {
                        TimetableItem timetableItem = new TimetableItem();
                        timetableItem.setDueDate(engineScheduleLine.getDueDate());
                        timetableItem.setStartDate(engineScheduleLine.getStartDate());
                        timetableItem.setEndDate(engineScheduleLine.getEndDate());
                        timetableItem.setUnpaid(engineScheduleLine.getUnpaid());
                        timetableItem.setDepreciation(engineScheduleLine.getAmortization());
                        timetableItem.setInterest(engineScheduleLine.getInterest());
                        timetableItem.setRate(engineScheduleLine.getRate());
						timetableItem.setNominalRate(engineScheduleLine.getNominalRate());
                        timetableItem.setRent(engineScheduleLine.getRent());
                        timetableItem.setTaxAmount(engineScheduleLine.getTaxAmount());
                        timetableItem.setResidualValue(engineScheduleLine.getCumulativeResidualValue());
						timetableItem.setStatus("INITIALIZED");
                        timetableItem.setTimetableId(new Timetable(finalLastRentalDto));
                        return timetableItem;
                    })
                    .toList();
            timetableItemRepository.saveAll(timetableItems);
            ResponseEntity.ok(PageDto.<TimetableItemDto>builder().data(timetableItems.stream()
                    .map(timetableItem -> modelMapper.map(timetableItem, TimetableItemDto.class)).toList()).build());
        } else {
            throw new BusinessException("error creating schedule", MODULE);
        }
    }

    @Transactional
    public ResponseEntity<PageDto<TimetableItemDto>> getAllRentalsSchedule(Long id) {
        List<Rental> rentals =
                rentalRepository.findBySimulationActorIdSimulationIdAndDeletedAtIsNullOrderByCreatedAtDesc(id);
        List<TimetableItemDto> timetableItemDtoList = new ArrayList<>();
        for (Rental rental : rentals) {
            List<TimetableItem> timetableItems = timetableItemRepository.findByTimetableIdId(
                    rental.getTimetableId().getId());
            List<TimetableItemDto> timetableItemsDtos = timetableItems.stream()
                    .map(timetableItem -> {
                        TimetableItemDto timetableItemDto = modelMapper.map(timetableItem, TimetableItemDto.class);
                        timetableItemDto.setType("RENTAL");
                        if (rental.getLineType() != null)
                            timetableItemDto.setLineTypeLabel(rental.getLineType().getLabel());
                        return timetableItemDto;
                    })
                    .toList();
            timetableItemDtoList.addAll(timetableItemsDtos);
        }
        timetableItemDtoList.sort(
                Comparator.comparing((TimetableItemDto timetableItemDto) -> timetableItemDto.getTimetableId().getId())
                        .thenComparing(TimetableItemDto::getStartDate));
        return ResponseEntity.ok(
                PageDto.<TimetableItemDto>builder().data(timetableItemDtoList).total(timetableItemDtoList.size()).build());
    }

    private List<EngineScheduleLine> generateSchedule(RentalDto rentalDto) {
        EngineRubriqueFrontDto rubriqueFrontDto = getEngineRubriqueFrontDto(rentalDto);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<EngineRubriqueFrontDto> requestEntity = new HttpEntity<>(rubriqueFrontDto, headers);

        ResponseEntity<PageDto<EngineScheduleLine>> schedule = restTemplate.exchange(
                ENGINE_SERVER_URL + "finance-calculator/calculate-schedule",
                HttpMethod.POST,
                requestEntity,
                new ParameterizedTypeReference<PageDto<EngineScheduleLine>>() {
                }
        );

        if (schedule.getStatusCode() == HttpStatus.OK) {
            return Objects.requireNonNull(schedule.getBody()).getData();
        }

        return Collections.emptyList();
    }


    private boolean isRentalSectionValid(RentalDto rentalDto) {
        EngineRubriqueFrontDto rubriqueFrontDto = getEngineRubriqueFrontDto(rentalDto);


        ResponseEntity<SingleResultDto<EngineRateCalculation>> response = restTemplate.exchange(
                ENGINE_SERVER_URL + "finance-calculator/calculate-rate",
                HttpMethod.POST,
                new HttpEntity<>(rubriqueFrontDto), new ParameterizedTypeReference<>() {
                }
        );

        return response.getStatusCode() == HttpStatus.OK
                && rentalDto.getNominalRate()
                == Objects.requireNonNull(response.getBody()).getData().getNominalRate() * 100;
    }

    private static EngineRubriqueFrontDto getEngineRubriqueFrontDto(RentalDto rentalDto) {
        EngineRubriqueFrontDto rubriqueFrontDto = getRubriqueFrontDto(rentalDto);
        for (int i = 0; i < rentalDto.getRentalLevelsList().size(); i++) {
            EngineTimetableLevelFrontDto timetableLevel = new EngineTimetableLevelFrontDto();
            LevelDto levelDto = rentalDto.getRentalLevelsList().get(i);
            timetableLevel.setOrder(i + 1);
			timetableLevel.setStartDate(levelDto.getStartDate());
            timetableLevel.setTypeUnitePeriode(levelDto.getPeriod());
            timetableLevel.setFrequency(1);
            timetableLevel.setDuration(levelDto.getPeriodNumber());
            timetableLevel.setAmount(levelDto.getRent());
            timetableLevel.setMultiple(levelDto.getPeriodMultiple());
            rubriqueFrontDto.getTimetableLevelFrontDtoList().add(timetableLevel);
        }
        return rubriqueFrontDto;
    }

    private static EngineRubriqueFrontDto getRubriqueFrontDto(RentalDto rentalDto) {
        EngineRubriqueFrontDto rubriqueFrontDto = new EngineRubriqueFrontDto();
		rubriqueFrontDto.setBase(rentalDto.getCalculationBasis());
		rubriqueFrontDto.setDecompte(rentalDto.getCalculationMode());
        rubriqueFrontDto.setFinancialAmount(rentalDto.getOriginalAmount());
		rubriqueFrontDto.setStartDate(OfferUtils.convertToDate(rentalDto.getStartDate()));
		rubriqueFrontDto.setEndDate(OfferUtils.convertToDate(rentalDto.getEndDate()));
        rubriqueFrontDto.setTaxValue(rentalDto.getTaxRate() == null ? 0.0 : rentalDto.getTaxRate());
        rubriqueFrontDto.setMultiple(1);
        rubriqueFrontDto.setRate(rentalDto.getNominalRate() == null ? 0.0 : rentalDto.getNominalRate());
        rubriqueFrontDto.setRatePeriod(TypeUnitePeriode.ENUM_MOIS);
        rubriqueFrontDto.setResidualValue(rentalDto.getResidualValue());
        return rubriqueFrontDto;
    }

    private void validateRentalAgainstScale(RentalDto rentalDto, Scale scale, SimulationActor simulationActor) {
        List<LevelDto> rentalLevels = new ArrayList<>(rentalDto.getRentalLevelsList());
        if (scale.getMinimumInterestRate() != null && scale.getMaximumInterestRate() != null) {
            if (rentalDto.getNominalRate() < scale.getMinimumInterestRate() ||
                rentalDto.getNominalRate() > scale.getMaximumInterestRate()) {
                throw new ConflictException(
                    String.format("Nominal rate must be between %s and %s",
                        scale.getMinimumInterestRate(), scale.getMaximumInterestRate()),
                    "RENTAL_INVALID_NOMINAL_RATE",
                    "Invalid nominal rate",
                    MODULE);
            }
        }

        if (scale.getMinimumEligibleAmount() != null && scale.getMaximumEligibleAmount() != null) {
           if (rentalDto.getOriginalAmount() < scale.getMinimumEligibleAmount() ||
				   rentalDto.getOriginalAmount() > scale.getMaximumEligibleAmount()) {
				throw new ConflictException(
					String.format("Original amount must be between %s and %s",
						scale.getMinimumEligibleAmount(), scale.getMaximumEligibleAmount()),
					"RENTAL_INVALID_ORIGINAL_AMOUNT",
					"Invalid original amount",
					MODULE);
			}
        }

        if (scale.getMinimumResidualValue() != null && scale.getMaximumResidualValue() != null) {
            if (rentalDto.getResidualValue() < scale.getMinimumResidualValue() ||
                rentalDto.getResidualValue() > scale.getMaximumResidualValue()) {
                throw new ConflictException(
                    String.format("Residual value must be between %s and %s",
                        scale.getMinimumResidualValue(), scale.getMaximumResidualValue()),
                    "RENTAL_INVALID_RESIDUAL_VALUE",
                    "Invalid residual value",
                    MODULE);
            }
        }
        List<RecapSimulationElementDto> simulationElement = getSimulationElement(simulationActor.getSimulation().getId());
        if (!simulationElement.isEmpty() && simulationElement.getFirst().getElement() != null) {
            Vehicle vehicleElement = vehicleRepository.findByIdAndDeletedAtIsNull(simulationElement.getFirst().getElement().getId()).orElseThrow(
                () -> new ResourcesNotFoundException("Vehicle not found", MODULE));
            var scaleElementVehicle = simulationScaleService.getScaleElementVehicleByCode(scale.getCode());
            if (scaleElementVehicle.getBody() != null &&
                    scaleElementVehicle.getBody().getData() != null &&
                    scaleElementVehicle.getBody().getData().getMinimumMileage() != null &&
                    scaleElementVehicle.getBody().getData().getMaximumMileage() != null) {

                ScaleElementVehicleDto scaleElementVehicleDto = scaleElementVehicle.getBody().getData();
                if (vehicleElement.getMileage() < scaleElementVehicleDto.getMinimumMileage() ||
                        vehicleElement.getMileage() > scaleElementVehicleDto.getMaximumMileage()) {

                    throw new ConflictException(
                            String.format("Mileage must be between %s and %s",
                                    scaleElementVehicleDto.getMinimumMileage(), scaleElementVehicleDto.getMaximumMileage()),
                            "RENTAL_INVALID_MILEAGE",
                            "Invalid mileage",
                            MODULE);
                }
            }
        }

        if (scale.getHasPersonalContribution() != null && scale.getHasPersonalContribution() &&
            scale.getMinimumPersonalContribution() != null && scale.getMaximumPersonalContribution() != null) {

            if (rentalLevels.getFirst().getRent() < scale.getMinimumPersonalContribution() ||
                rentalLevels.getFirst().getRent() > scale.getMaximumPersonalContribution()) {
                throw new ConflictException(
                    String.format("Personal contribution must be between %s and %s",
                        scale.getMinimumPersonalContribution(), scale.getMaximumPersonalContribution()),
                    "RENTAL_INVALID_PERSONAL_CONTRIBUTION",
                    "Invalid personal contribution",
                    MODULE);
            }
			rentalLevels.removeFirst();
        }

		if (scale.getMaximumFinancingPeriod() != null && scale.getMinimumFinancingPeriod() != null &&
                scale.getRatePeriod() != null) {
			Integer[] financingPeriod = {0};
			rentalLevels.forEach((item) -> {
				if (item.getPeriod() != scale.getRatePeriod()) {
					throw new ConflictException(
						String.format("Periodicity must be %s", scale.getRatePeriod()),
						"RENTAL_INVALID_PERIODICITY",
						"Invalid periodicity",
						MODULE);
				}
                financingPeriod[0] += item.getPeriodNumber();
			});
			if (financingPeriod[0] < scale.getMinimumFinancingPeriod() ||
					financingPeriod[0] > scale.getMaximumFinancingPeriod()) {
				throw new ConflictException(
					String.format("Financing period must be between %s and %s",
						scale.getMinimumFinancingPeriod(), scale.getMaximumFinancingPeriod()),
					"RENTAL_INVALID_FINANCING_PERIOD",
					"Invalid financing period",
					MODULE);
			}
		}
    }

    private void setMileage(Long simulationId, RentalResponseDto rentalResponseDto, Double mileage) {
        List<RecapSimulationElementDto> simulationElement = getSimulationElement(simulationId);
        if (simulationElement.isEmpty()) return;
        Vehicle vehicleElement = vehicleRepository.findByIdAndDeletedAtIsNull(simulationElement.getFirst().getElement().getId()).orElseThrow(
                    () -> new ResourcesNotFoundException("Vehicle not found", MODULE));
            if (mileage == null) {
                mileage = vehicleElement.getMileage();
            }
            vehicleElement.setMileage(mileage);
            Vehicle savedVehicle = vehicleRepository.save(vehicleElement);
            rentalResponseDto.setMileage(savedVehicle.getMileage());
        }

    private List<RecapSimulationElementDto>  getSimulationElement(Long simulationId) {
        Map<String, String> params = new HashMap<>();
        params.put("PEqual_id", "" + simulationId);
        params.put("PEqual_nature_code", VEHICLE_NATURE_CODE);
        var allSimulationsRecap = simulationRecapService.getAllSimulationsRecap(params);
        if (allSimulationsRecap != null &&
                allSimulationsRecap.getBody() != null &&
                allSimulationsRecap.getBody().getData() != null &&
                !allSimulationsRecap.getBody().getData().isEmpty()) {
            return allSimulationsRecap.getBody().getData().getFirst().getSimulationElements();
        }
        return new ArrayList<>();
    }
}
