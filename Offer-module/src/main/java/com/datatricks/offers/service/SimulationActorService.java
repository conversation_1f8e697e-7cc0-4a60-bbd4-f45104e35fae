package com.datatricks.offers.service;

import com.datatricks.common.model.*;
import com.datatricks.common.model.dto.*;
import com.datatricks.common.repository.*;
import com.datatricks.common.utils.JpaQueryFilters;
import com.datatricks.offers.exception.ConflictException;
import com.datatricks.offers.exception.ResourcesNotFoundException;
import com.datatricks.offers.exception.handler.InformativeMessage;
import com.datatricks.offers.model.dto.SimulationActorFilesDto;
import com.datatricks.offers.params.AffectActorInput;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;

@Service
public class SimulationActorService {

    private final SimulationActorRepository simulationActorRepository;
    private final ModelMapper modelMapper;
    private final ActorRepository actorRepository;
    private final ActorRoleRepository actorRoleRepository;
    private final RoleRepository roleRepository;
    private final SimulationRepository simulationRepository;
    private final SimulationElementRepository simulationElementRepository;
    private final ElementRepository elementRepository;
    private static final String SIMULATION_NOT_FOUND = "Simulation not found";
    private static final String SIMULATION_ACTOR_NOT_FOUND = "Simulation Actor not found";
    private static final String ACTOR_NOT_FOUND = "Actor not found";

    private static final String ROLE_NOT_FOUND = "Role not found";
    private static final String ACTOR_IS_NOT_ACTIVE = "Actor must have active phase to be affected to a simulation";
    private static final String MODULE = "Simulation-Actor";
    private static final String RENT_ROLE = "RENT";
    private static final String CONTRACT_ACTOR_ROLE_CANNOT_BE_RENT = "Contract actor role cannot be rent";
    private static final String FILE_NAME_NOT_PROVIDED = "At least one file_name must be provided to update the actor.";
    private final SimulationActorPaymentsRepository simulationActorPaymentsRepository;
    private final ActorSettlementMeansRepository settlementMeansRepository;

    @Autowired
    public SimulationActorService(
            SimulationActorRepository simulationActorRepository,
            ModelMapper modelMapper,
            ActorRepository actorRepository,
            ActorRoleRepository actorRoleRepository,
            RoleRepository roleRepository,
            SimulationRepository simulationRepository,
            ElementRepository elementRepository,
            SimulationElementRepository simulationElementRepository,
            SimulationActorPaymentsRepository simulationActorPaymentsRepository,
            ActorSettlementMeansRepository settlementMeansRepository) {
        this.simulationActorRepository = simulationActorRepository;
        this.modelMapper = modelMapper;
        this.actorRepository = actorRepository;
        this.actorRoleRepository = actorRoleRepository;
        this.roleRepository = roleRepository;
        this.simulationRepository = simulationRepository;
        this.simulationElementRepository = simulationElementRepository;
        this.elementRepository = elementRepository;
        this.simulationActorPaymentsRepository = simulationActorPaymentsRepository;
        this.settlementMeansRepository = settlementMeansRepository;
    }

    @Transactional
    public ResponseEntity<PageDto<SimulationActorResponseDto>> createOrUpdateActorForSimulation(
            Long simulationId, AffectActorInput affectActorInput) {
        Simulation simulation = simulationRepository.findByIdAndDeletedAtIsNull(simulationId).orElseThrow(
                () -> new ResourcesNotFoundException(SIMULATION_NOT_FOUND, MODULE));

        List<SimulationActorResponseDto> simulationActorResponseDtos = new ArrayList<>();

        for (SimplifiedActorRoleDto actorRole : affectActorInput.getActors()) {
            Long actorId = actorRole.getActorId();
            String roleCode = actorRole.getRoleCode();

            Actor actor = actorRepository.findByIdAndDeletedAtIsNull(actorId).orElseThrow(
                    () -> new ResourcesNotFoundException(ACTOR_NOT_FOUND, MODULE));


            if (RENT_ROLE.equals(roleCode)) {
                throw new ConflictException(
                        CONTRACT_ACTOR_ROLE_CANNOT_BE_RENT,
                        "CONTRACT_ACTOR_ROLE_CANNOT_BE_RENT",
                        CONTRACT_ACTOR_ROLE_CANNOT_BE_RENT,
                        MODULE
                );
            }

            if (!Objects.equals(actor.getPhase().getCode(), "ACTIVE")) {
                throw new ConflictException(
                        ACTOR_IS_NOT_ACTIVE,
                        "ACTOR_IS_NOT_ACTIVE",
                        ACTOR_IS_NOT_ACTIVE,
                        MODULE);
            }

            // TODO: static role code should be replaced with role code
            if (actorRoleRepository.findByActorIdIdAndRoleCodeAndDeletedAtIsNull(actorId, roleCode).isEmpty()) {
                ActorRole newActorRole = new ActorRole();
                newActorRole.setActorId(actor);
                newActorRole.setRole(roleRepository.findByCode(roleCode).orElseThrow(
                        () -> new ResourcesNotFoundException(ROLE_NOT_FOUND, MODULE)));
                newActorRole.setIsPrincipal(false);
                actorRoleRepository.save(newActorRole);
            }
            SimulationActorResponseDto responseDto = saveOrUpdateActorWithRole(simulation, actor, roleCode);
            simulationActorResponseDtos.add(responseDto);
        }

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(PageDto.<SimulationActorResponseDto>builder()
                        .data(simulationActorResponseDtos)
                        .build());
    }


    @Transactional
    protected SimulationActorResponseDto saveOrUpdateActorWithRole(Simulation simulation, Actor actor, String roleCode) {
        Role role = roleRepository.findByCode(roleCode)
                .orElseThrow(() -> new ResourcesNotFoundException(ROLE_NOT_FOUND, MODULE));

        SimulationActor simulationActor = simulationActorRepository
                .findBySimulationIdAndRoleStaticRoleCodeCodeAndDeletedAtIsNull(simulation.getId(), roleCode)
                .orElseGet(SimulationActor::new);

        if (simulationActor.getId() != null) {
            simulationActor.setModifiedAt(new Date());
        } else {
            simulationActor.setRole(role);
            simulationActor.setSimulation(simulation);
            simulationActor.setDelegation(new Delegation(3L)); //TODO replace this when delegation is impleted in front
        }

        simulationActor.setActor(actor);
        simulationActorRepository.save(simulationActor);
        associateDefaultPaymentMethods(simulationActor, actor);

        if("CLIENT".equals(role.getCode())) {
            updateElementClient(actor,simulation);
        }

        SimulationActorResponseDto responseDto = modelMapper.map(simulationActor, SimulationActorResponseDto.class);
        responseDto.setRole(modelMapper.map(role, RoleDto.class));

        return responseDto;
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteActorsForSimulation(Long simulationId, AffectActorInput affectActorInput) {
        for (SimplifiedActorRoleDto actorRole : affectActorInput.getActors()) {
            Long actorId = actorRole.getActorId();
            String roleCode = actorRole.getRoleCode();

            SimulationActor simulationActor = simulationActorRepository.findBySimulationIdAndActorIdAndRoleStaticRoleCodeCodeAndDeletedAtIsNull(
                    simulationId, actorId, roleCode).orElseThrow(
                    () -> new ResourcesNotFoundException(SIMULATION_ACTOR_NOT_FOUND, MODULE));
            simulationActor.setDeletedAt(new Date());
            simulationActorRepository.save(simulationActor);
        }

        return ResponseEntity.ok(new InformativeMessage("Resources have been deleted successfully"));
    }

    @Transactional
    public ResponseEntity<PageDto<SimulationActorResponseDto>> getSimulationActors(Long id, Map<String, String> params) {
        JpaQueryFilters<SimulationActor> filters = new JpaQueryFilters<>(params, SimulationActor.class);
        Specification<SimulationActor> simulationIdSpec = (root, query, criteriaBuilder) -> criteriaBuilder.and(
                criteriaBuilder.equal(root.get("simulation").get("id"), id)
        );

        Page<SimulationActor> page =
                simulationActorRepository.findAll(filters.getSpecification().and(simulationIdSpec), filters.getPageable());
        List<SimulationActorResponseDto> simulationActorResponseDtos = new ArrayList<>();
        for (SimulationActor simulationActor : page) {
            SimulationActorResponseDto simulationDto = modelMapper.map(simulationActor, SimulationActorResponseDto.class);
            simulationActorResponseDtos.add(simulationDto);
        }

        return new ResponseEntity<>(
                PageDto.<SimulationActorResponseDto>builder()
                        .data(simulationActorResponseDtos)
                        .total(page.getTotalElements())
                        .build(),
                HttpStatus.OK);
    }

    @Transactional
    public ResponseEntity<PageDto<SimulationActorResponseDto>> updateActorForSimulation(
            Long simulationId, AffectActorInput affectActorInput) {
        Simulation simulation = simulationRepository.findByIdAndDeletedAtIsNull(simulationId).orElseThrow(
                () -> new ResourcesNotFoundException(SIMULATION_NOT_FOUND, MODULE));

        List<SimulationActorResponseDto> simulationActorResponseDtos = new ArrayList<>();

        for (SimplifiedActorRoleDto actorRole : affectActorInput.getActors()) {
            Long actorId = actorRole.getActorId();
            String roleCode = actorRole.getRoleCode();

            Actor actor = actorRepository.findByIdAndDeletedAtIsNull(actorId).orElseThrow(
                    () -> new ResourcesNotFoundException(ACTOR_NOT_FOUND, MODULE));

            Address address = actor.getAddresses().stream().findFirst().orElse(null);

            if (RENT_ROLE.equals(roleCode)) {
                throw new ConflictException(
                        CONTRACT_ACTOR_ROLE_CANNOT_BE_RENT,
                        "CONTRACT_ACTOR_ROLE_CANNOT_BE_RENT",
                        CONTRACT_ACTOR_ROLE_CANNOT_BE_RENT,
                        MODULE
                );
            }

            if (!Objects.equals(actor.getPhase().getCode(), "ACTIVE")) {
                throw new ConflictException(
                        ACTOR_IS_NOT_ACTIVE,
                        "ACTOR_IS_NOT_ACTIVE",
                        ACTOR_IS_NOT_ACTIVE,
                        MODULE);
            }

            if (actorRoleRepository.findByActorIdIdAndRoleStaticRoleCodeCodeAndDeletedAtIsNull(actorId, roleCode).isEmpty()) {
                ActorRole newActorRole = new ActorRole();
                newActorRole.setActorId(actor);
                newActorRole.setRole(roleRepository.findByCode(roleCode).orElseThrow(
                        () -> new ResourcesNotFoundException(ROLE_NOT_FOUND, MODULE)));
                newActorRole.setIsPrincipal(false);
                actorRoleRepository.save(newActorRole);
            }

            SimulationActorResponseDto responseDto = saveOrUpdateActorWithRole(simulation, actor, roleCode);
            simulationActorResponseDtos.add(responseDto);
        }
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(PageDto.<SimulationActorResponseDto>builder()
                        .data(simulationActorResponseDtos)
                        .build());
    }

    @Transactional
    protected void updateElementClient(Actor actor, Simulation simulation) {

        List<SimulationElement> simulationElements = simulationElementRepository.findAllBySimulationIdAndDeletedAtIsNull(simulation.getId());

        for (SimulationElement simulationElement : simulationElements) {
            Element element = simulationElement.getElement();

            element.setClient(actor);
            elementRepository.save(element);
        }

        simulation.setLegalactivity(actor.getActivity());
        simulation.setActorType(actor.getType());
        simulation.setFeedChannel(actor.getFeedChannel());
        simulationRepository.save(simulation);
    }


    @Transactional
    public ResponseEntity<InformativeMessage> updateSimulationActorFile(Long simulationActorId, SimulationActorFilesDto filesDto) {
        SimulationActor simulationActor = simulationActorRepository.findById(simulationActorId)
                .orElseThrow(() -> new ResourcesNotFoundException(SIMULATION_ACTOR_NOT_FOUND, MODULE));

        boolean updated = false;

        if (filesDto.getPid() != null) { simulationActor.setPid(filesDto.getPid()); updated = true; }
        if (filesDto.getExtraitKbis() != null) { simulationActor.setExtraitKbis(filesDto.getExtraitKbis()); updated = true; }
        if (filesDto.getBilan() != null) { simulationActor.setBilan(filesDto.getBilan()); updated = true; }
        if (filesDto.getCni() != null) { simulationActor.setCni(filesDto.getCni()); updated = true; }
        if (filesDto.getRib() != null) { simulationActor.setRib(filesDto.getRib()); updated = true; }
        if (filesDto.getMandatSepa() != null) { simulationActor.setMandatSepa(filesDto.getMandatSepa()); updated = true; }

        if (!updated) {
            throw new ResourcesNotFoundException(FILE_NAME_NOT_PROVIDED, MODULE);
        }


        return ResponseEntity.ok(new InformativeMessage("One simulation actor file has been successfully updated."));
    }

    @jakarta.transaction.Transactional
    protected void associateDefaultPaymentMethods(SimulationActor simulationActor, Actor actor) {
        List<SettlementMeans> settlementMeansList = settlementMeansRepository.findByActorIdAndDeletedAtIsNull(actor.getId());
        for (SettlementMeans settlementMeans : settlementMeansList) {
            PaymentMethod paymentMethod = settlementMeans.getPaymentMethod();
            if (paymentMethod != null) {
                SimulationActorPayments simulationActorPayments = new SimulationActorPayments();
                simulationActorPayments.setSimulationActor(simulationActor);
                simulationActorPayments.setPaymentMethod(paymentMethod);
                simulationActorPayments.setType(settlementMeans.getPaymentType());
                simulationActorPayments.setStartDate(LocalDate.now()); // TODO : check if this is correct start date
                simulationActorPaymentsRepository.save(simulationActorPayments);
            }
        }
    }
}
