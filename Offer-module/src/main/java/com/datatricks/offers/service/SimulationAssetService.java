package com.datatricks.offers.service;

import com.datatricks.common.model.*;
import com.datatricks.common.model.Currency;
import com.datatricks.common.model.dto.ElementDto;
import com.datatricks.common.model.dto.PageDto;
import com.datatricks.common.model.dto.SingleResultDto;
import com.datatricks.common.repository.*;
import com.datatricks.common.utils.JpaQueryFilters;
import com.datatricks.offers.exception.BusinessException;
import com.datatricks.offers.exception.ResourcesNotFoundException;
import com.datatricks.offers.exception.handler.InformativeMessage;
import com.datatricks.offers.model.dto.*;
import com.datatricks.offers.repository.CustomSimulationRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;

import static com.datatricks.offers.utils.OfferUtils.createReference;

@Service
@RequiredArgsConstructor
public class SimulationAssetService {

    private static final String OFFER_NOT_FOUND = "Offer not found";
    private static final String OFFER_REF_PREFIX = "OFFER_";
    private static final String SUPPLIER_NOT_FOUND = "Supplier not found";
    private static final String MODULE = "Offer";
    private static final String ASSOCIATED_TO_NATURE = "NATURE";
    private static final String ASSET_STATUS = "draft_asset";
    private final OfferRepository offerRepository;
    private final ActorRepository actorRepository;
    private final ElementRepository elementRepository;
    private final VehicleRepository vehicleRepository;
    private final SimulationRepository simulationRepository;
    private final CustomSimulationRepository customSimulationRepository;
    private final PhaseRepository phaseRepository;
    private final MilestoneRepository milestoneRepository;
    private final ActivityRepository activityRepository;
    private final ProductRepository productRepository;
    private final SimulationElementRepository simulationElementRepository;
    private final StaticParameterRepository staticParameterRepository;
    private final SimulationActorRepository simulationActorRepository;
    private final MaterialRepository materialRepository;
    private final VehicleStockRepository vehicleStockRepository;
    private final TaxeRateRepository taxeRateRepository;
    private final CategoryRepository categoryRepository;
    private final CurrencyRepository currencyRepository;
    private final RentalRepository rentalRepository;
    private final ModelMapper modelMapper;
    private final MarketRepository marketRepository;
    private final EquipmentRepository equipmentRepository;
    private final SimulationActorPaymentsRepository simulationActorPaymentsRepository;
    private final ActorSettlementMeansRepository settlementMeansRepository;

    @Transactional
    public ResponseEntity<SingleResultDto<SimulationResponseDto>> createSimulationVehicle(VehicleRequestDto vehicleRequestDto) {

        TotalPriceCalculationResultDto totalPriceCalculationResultDto = calculateTotalPricesVehicle(vehicleRequestDto);

        Offer offer = offerRepository.save(getOrCreateOffer(vehicleRequestDto.getOfferReference()));

        Simulation simulation = simulationRepository.findByIdAndDeletedAtIsNull(vehicleRequestDto.getSimulationId())
                .orElseGet(Simulation::new);

        Product product = productRepository.findByCode(vehicleRequestDto.getProductCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Product not found", MODULE));

        Actor business = actorRepository.findByReferenceAndTypeAndDeletedAtIsNull(
                        vehicleRequestDto.getBusinessReference(), ActorTypes.MANAGEMENT_COMPANY)
                .orElseThrow(() -> new ResourcesNotFoundException("Business not found", MODULE));

        Activity activity = activityRepository.findByCode(vehicleRequestDto.getActivityCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Activity not found", MODULE));

        Actor actor = actorRepository.findByReferenceAndDeletedAtIsNull(vehicleRequestDto.getSupplierCode())
                .orElseThrow(() -> new ResourcesNotFoundException(SUPPLIER_NOT_FOUND, MODULE));

        StaticParameter nature = staticParameterRepository.findByCodeAndAssociatedTo(vehicleRequestDto.getNatureCode(), ASSOCIATED_TO_NATURE)
                .orElseThrow(() -> new ResourcesNotFoundException("Nature not found", MODULE));


        Vehicle vehicle = new Vehicle();
        buildVehicleProperties(vehicle, vehicleRequestDto, totalPriceCalculationResultDto, actor, nature);
        vehicleRepository.save(vehicle);


        buildSimulationProperties(simulation, nature, business, activity, product, offer);
        buildPriceSimulationVehicle(simulation, totalPriceCalculationResultDto);
        simulation = simulationRepository.save(simulation);

        SimulationElement simulationElement = SimulationElement.builder().simulation(simulation).element(vehicle).build();
        buildSimulationElementVehicle(simulationElement, vehicleRequestDto, totalPriceCalculationResultDto, vehicle);
        simulationElementRepository.save(simulationElement);

        SimulationActor simulationActor = simulationActorRepository.findBySimulationIdAndRoleStaticRoleCodeCodeAndDeletedAtIsNull(simulation.getId(), "FOURN")
                .orElseGet(SimulationActor::new);

        buildSimulationActor(simulationActor, simulation, actor);
        simulationActorRepository.save(simulationActor);
        associateDefaultPaymentMethods(simulationActor, actor);

        List<SimulationElement> simulationElements = simulationElementRepository.findAllBySimulationIdAndDeletedAtIsNull(simulation.getId());
        SimulationResponseDto simulationResponseDto = buildSimulationRequestDto(simulation, simulationElements, offer);

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(SingleResultDto.<SimulationResponseDto>builder().data(simulationResponseDto).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<SimulationResponseDto>> createSimulationMaterial(MaterialRequestDto materialRequestDto) {

        Offer offer = offerRepository.save(getOrCreateOffer(materialRequestDto.getOfferReference()));

        Product product = productRepository.findByCode(materialRequestDto.getProductCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Product not found", MODULE));

        Actor business = actorRepository.findByReferenceAndTypeAndDeletedAtIsNull(
                        materialRequestDto.getBusinessReference(), ActorTypes.MANAGEMENT_COMPANY)
                .orElseThrow(() -> new ResourcesNotFoundException("Business not found", MODULE));

        Activity activity = activityRepository.findByCode(materialRequestDto.getActivityCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Activity not found", MODULE));

        Actor actor = actorRepository.findByReferenceAndDeletedAtIsNull(materialRequestDto.getSupplierCode())
                .orElseThrow(() -> new ResourcesNotFoundException(SUPPLIER_NOT_FOUND, MODULE));

        StaticParameter nature = staticParameterRepository.findByCodeAndAssociatedTo(materialRequestDto.getNatureCode(), ASSOCIATED_TO_NATURE)
                .orElseThrow(() -> new ResourcesNotFoundException("Nature not found", MODULE));

        Material material = materialRepository.findByIdAndDeletedAtIsNull(materialRequestDto.getElementId())
                .orElseThrow(() -> new ResourcesNotFoundException("Element not found", MODULE));

        buildMaterialProperties(material, materialRequestDto);
        elementRepository.save(material);

        Simulation simulation = new Simulation();
        buildSimulationProperties(simulation, nature, business, activity, product, offer);
        buildPriceSimulationMaterial(simulation, material, materialRequestDto, true);
        simulation = simulationRepository.save(simulation);

        SimulationElement simulationElement = SimulationElement.builder().simulation(simulation).element(material).build();
        buildSimulationElementMaterial(simulationElement, material);
        simulationElementRepository.save(simulationElement);


        SimulationActor simulationActor = new SimulationActor();
        buildSimulationActor(simulationActor, simulation, actor);
        simulationActorRepository.save(simulationActor);
        associateDefaultPaymentMethods(simulationActor, actor);

        List<SimulationElement> simulationElements = simulationElementRepository.findAllBySimulationIdAndDeletedAtIsNull(simulation.getId());
        SimulationResponseDto simulationResponseDto = buildSimulationRequestDto(simulation, simulationElements, offer);

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(SingleResultDto.<SimulationResponseDto>builder().data(simulationResponseDto).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<SimulationResponseDto>> createSimulationElementMaterial(MaterialRequestDto materialRequestDto) {

        Simulation simulation = simulationRepository.findByIdAndDeletedAtIsNull(materialRequestDto.getSimulationId())
                .orElseThrow(() -> new ResourcesNotFoundException("Simulation not found", MODULE));

        Offer offer = offerRepository.findByIdAndDeletedAtIsNull(simulation.getOffer().getId())
                .orElseThrow(() -> new ResourcesNotFoundException(OFFER_NOT_FOUND, MODULE));

        if (!simulation.getOffer().getId().equals(offer.getId())) {
            throw new BusinessException("The simulation and offer are not related", "0");
        }

        // Validate the simulation  relationship (product, business, activity)
        validateSimulation(materialRequestDto, simulation);

        Material material = materialRepository.findByIdAndDeletedAtIsNull(materialRequestDto.getElementId())
                .orElseThrow(() -> new ResourcesNotFoundException("Element not found", MODULE));

        buildMaterialProperties(material, materialRequestDto);
        elementRepository.save(material);

        buildPriceSimulationMaterial(simulation, material, materialRequestDto, true);
        simulation = simulationRepository.save(simulation);

        SimulationElement simulationElement = SimulationElement.builder().simulation(simulation).element(material).build();
        buildSimulationElementMaterial(simulationElement, material);
        simulationElementRepository.save(simulationElement);

        Actor supplier = actorRepository.findByReferenceAndDeletedAtIsNull(materialRequestDto.getSupplierCode())
                .orElseThrow(() -> new ResourcesNotFoundException(SUPPLIER_NOT_FOUND, MODULE));

        Optional<SimulationActor> existingActor = simulationActorRepository
                .findBySimulationIdAndActorIdAndRoleStaticRoleCodeCodeAndDeletedAtIsNull(
                        simulation.getId(), supplier.getId(), "FOURN");

        if (existingActor.isEmpty()) {
            SimulationActor simulationActor = new SimulationActor();
            buildSimulationActor(simulationActor, simulation, supplier);
            simulationActorRepository.save(simulationActor);
            associateDefaultPaymentMethods(simulationActor, supplier);
        }

        List<SimulationElement> simulationElements = simulationElementRepository.findAllBySimulationIdAndDeletedAtIsNull(simulation.getId());
        SimulationResponseDto simulationResponseDto = buildSimulationRequestDto(simulation, simulationElements, offer);
        return ResponseEntity.ok(SingleResultDto.<SimulationResponseDto>builder().data(simulationResponseDto).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<SimulationResponseDto>> updateSimulationVehicle(VehicleRequestDto vehicleRequestDto) {

        TotalPriceCalculationResultDto totalPriceCalculationResultDto = calculateTotalPricesVehicle(vehicleRequestDto);

        Offer offer = offerRepository.findByReferenceAndDeletedAtIsNull(vehicleRequestDto.getOfferReference())
                .orElseThrow(() -> new ResourcesNotFoundException(OFFER_NOT_FOUND, MODULE));

        Simulation simulation = simulationRepository.findByIdAndDeletedAtIsNull(vehicleRequestDto.getSimulationId())
                .orElseThrow(() -> new ResourcesNotFoundException("Simulation not found", MODULE));

        if (!simulation.getOffer().getId().equals(offer.getId())) {
            throw new BusinessException("The simulation and offer are not related", "0");
        }

        SimulationElement simulationElement = simulationElementRepository.findByIdAndDeletedAtIsNull(vehicleRequestDto.getSimulationElementId())
                .orElseThrow(() -> new ResourcesNotFoundException("This simulation is not associated to any element", MODULE));


        Actor business = actorRepository.findByReferenceAndTypeAndDeletedAtIsNull(vehicleRequestDto.getBusinessReference(), ActorTypes.MANAGEMENT_COMPANY)
                .orElseThrow(() -> new ResourcesNotFoundException("Business not found", MODULE));

        Activity activity = activityRepository.findByCode(vehicleRequestDto.getActivityCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Activity not found", MODULE));

        Vehicle vehicle = vehicleRepository.findByIdAndDeletedAtIsNull(vehicleRequestDto.getElementId())
                .orElseThrow(() -> new ResourcesNotFoundException("Element not found", MODULE));

        if (!simulationElement.getSimulation().getId().equals(simulation.getId()) ||
                !simulationElement.getElement().getId().equals(vehicle.getId())) {
            throw new BusinessException("The simulation and its elements are not related", "0");
        }

        StaticParameter nature = staticParameterRepository.findByCodeAndAssociatedTo(vehicleRequestDto.getNatureCode(), ASSOCIATED_TO_NATURE)
                .orElseThrow(() -> new ResourcesNotFoundException("Nature not found", MODULE));

        Product product = productRepository.findByCode(vehicleRequestDto.getProductCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Product not found", MODULE));

        Actor actor = actorRepository.findByReferenceAndDeletedAtIsNull(vehicleRequestDto.getSupplierCode())
                .orElseThrow(() -> new ResourcesNotFoundException(SUPPLIER_NOT_FOUND, MODULE));


        buildVehicleProperties(vehicle, vehicleRequestDto, totalPriceCalculationResultDto, actor, nature);
        vehicleRepository.save(vehicle);

        buildSimulationProperties(simulation, nature, business, activity, product, offer);
        buildPriceSimulationVehicle(simulation, totalPriceCalculationResultDto);
        simulation = simulationRepository.save(simulation);

        simulationElement.setElement(vehicle);
        buildSimulationElementVehicle(simulationElement, vehicleRequestDto, totalPriceCalculationResultDto, vehicle);
        simulationElementRepository.save(simulationElement);

        SimulationActor simulationActor = simulationActorRepository.findBySimulationIdAndRoleStaticRoleCodeCodeAndDeletedAtIsNull(simulation.getId(), "FOURN")
                .orElseGet(SimulationActor::new);

        buildSimulationActor(simulationActor, simulation, actor);
        simulationActorRepository.save(simulationActor);
        associateDefaultPaymentMethods(simulationActor, actor);

        List<SimulationElement> simulationElements = simulationElementRepository.findAllBySimulationIdAndDeletedAtIsNull(simulation.getId());
        SimulationResponseDto simulationResponseDto = buildSimulationRequestDto(simulation, simulationElements, offer);

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(SingleResultDto.<SimulationResponseDto>builder().data(simulationResponseDto).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<SimulationResponseDto>> updateSimulationElementMaterial(MaterialRequestDto materialRequestDto) {

        Simulation simulation = simulationRepository.findByIdAndDeletedAtIsNull(materialRequestDto.getSimulationId())
                .orElseThrow(() -> new ResourcesNotFoundException("Simulation not found", MODULE));

        Offer offer = offerRepository.findByIdAndDeletedAtIsNull(simulation.getOffer().getId())
                .orElseThrow(() -> new ResourcesNotFoundException(OFFER_NOT_FOUND, MODULE));

        if (!simulation.getOffer().getId().equals(offer.getId())) {
            throw new BusinessException("The simulation and offer are not related", "0");
        }

        // Validate the simulation  relationship (product, business, activity)
        validateSimulation(materialRequestDto, simulation);

        SimulationElement simulationElement = simulationElementRepository.findByIdAndDeletedAtIsNull(materialRequestDto.getSimulationElementId())
                .orElseThrow(() -> new ResourcesNotFoundException("This simulation is not associated to any element", MODULE));

        if (!simulationElement.getSimulation().getId().equals(simulation.getId())) {
            throw new BusinessException("The simulation and simulationElement are not related", "0");
        }

        Material material = materialRepository.findByIdAndDeletedAtIsNull(materialRequestDto.getElementId())
                .orElseThrow(() -> new ResourcesNotFoundException("Element not found", MODULE));

        buildMaterialProperties(material, materialRequestDto);
        elementRepository.save(material);

        buildPriceSimulationMaterial(simulation, material, materialRequestDto, false);
        simulation = simulationRepository.save(simulation);

        simulationElement.setElement(material);
        buildSimulationElementMaterial(simulationElement, material);
        simulationElementRepository.save(simulationElement);

        Actor supplier = actorRepository.findByReferenceAndDeletedAtIsNull(materialRequestDto.getSupplierCode())
                .orElseThrow(() -> new ResourcesNotFoundException(SUPPLIER_NOT_FOUND, MODULE));

        Optional<SimulationActor> existingActor = simulationActorRepository
                .findBySimulationIdAndActorIdAndRoleStaticRoleCodeCodeAndDeletedAtIsNull(
                        simulation.getId(), supplier.getId(), "FOURN");

        if (existingActor.isEmpty()) {
            SimulationActor simulationActor = new SimulationActor();
            buildSimulationActor(simulationActor, simulation, supplier);
            simulationActorRepository.save(simulationActor);
            associateDefaultPaymentMethods(simulationActor, supplier);
        }

        List<SimulationElement> simulationElements = simulationElementRepository.findAllBySimulationIdAndDeletedAtIsNull(simulation.getId());
        SimulationResponseDto simulationResponseDto = buildSimulationRequestDto(simulation, simulationElements, offer);
        return ResponseEntity.ok(SingleResultDto.<SimulationResponseDto>builder().data(simulationResponseDto).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<SimulationResponseDto>> getSimulation(Long simulationId) {
        Simulation simulation = simulationRepository.findByIdAndDeletedAtIsNull(simulationId)
                .orElseThrow(() -> new ResourcesNotFoundException("Simulation not found with ID: " + simulationId, MODULE));

        Offer offer = offerRepository.findByIdAndDeletedAtIsNull(simulation.getOffer().getId())
                .orElseThrow(() -> new ResourcesNotFoundException("Offer not found for simulation ID: " + simulationId, MODULE));

        List<SimulationElement> simulationElements = simulationElementRepository.findAllBySimulationIdAndDeletedAtIsNull(simulationId);

        SimulationResponseDto simulationResponseDto = buildSimulationRequestDto(simulation, simulationElements, offer);

        return ResponseEntity.ok(SingleResultDto.<SimulationResponseDto>builder().data(simulationResponseDto).build());
    }

    @Transactional
    public ResponseEntity<PageDto<SimulationDetailDto>> getSimulations(Map<String, String> params) {
        JpaQueryFilters<Simulation> filters = new JpaQueryFilters<>(params, Simulation.class);
        Page<Object[]> simDetails =
                customSimulationRepository.findSimulationsWithDetails(params.get("title"), filters.getPageable());

        List<SimulationDetailDto> data = simDetails.stream().map(simulation ->
                new SimulationDetailDto(
                        (Long) simulation[0],
                        (String) simulation[1],
                        (String) simulation[2],
                        (String) simulation[3],
                        (String) simulation[4],
                        (String) simulation[5],
                        (String) simulation[6],
                        (String) simulation[7],
                        (String) simulation[8],
                        (Date) simulation[9],
                        (Date) simulation[10],
                        (Double) simulation[11]
                )).toList();

        return ResponseEntity.ok(PageDto.<SimulationDetailDto>builder().data(data).total(simDetails.getTotalElements())
                .build());
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteSimulationElement(Long simulationElementId) {

        SimulationElement simulationElement = simulationElementRepository.findByIdAndDeletedAtIsNull(simulationElementId)
                .orElseThrow(() -> new ResourcesNotFoundException("SimulationElement not found", MODULE));

        Simulation simulation = simulationElement.getSimulation();

        simulationElement.setDeletedAt(new Date());
        simulationElementRepository.save(simulationElement);

        updateSimulationTotals(simulation, simulationElement);

        if (simulationElement.getElement() != null && simulationElement.getElement().getSupplier() != null) {
            Long supplierId = simulationElement.getElement().getSupplier().getId();

            // Check if there are other elements in the simulation with the same supplier
            boolean hasOtherElements = simulationElementRepository.findAllBySimulationIdAndDeletedAtIsNull(simulation.getId())
                    .stream()
                    .anyMatch(se -> se.getElement() != null &&
                            se.getElement().getSupplier() != null &&
                            se.getElement().getSupplier().getId().equals(supplierId));

            if (!hasOtherElements) {
                simulationActorRepository.findBySimulationIdAndActorIdAndRoleStaticRoleCodeCodeAndDeletedAtIsNull(
                                simulation.getId(), supplierId, "FOURN")
                        .ifPresent(simulationActor -> {
                            simulationActor.setDeletedAt(new Date());
                            simulationActorRepository.save(simulationActor);
                        });
            }
        }

        return ResponseEntity.ok(new InformativeMessage("SimulationElement deleted successfully"));
    }
    @Transactional
    protected void updateSimulationTotals(Simulation simulation, SimulationElement simulationElement) {
        double elementPrice = simulationElement.getElement() != null && simulationElement.getElement().getPrice() != null
                ? simulationElement.getElement().getPrice() : 0.0;
        double elementTotalPrice = simulationElement.getElement() != null && simulationElement.getElement().getTotalPrice() != null
                ? simulationElement.getElement().getTotalPrice() : 0.0;
        int elementQuantity = simulationElement.getQuantity() != null ? simulationElement.getQuantity() : 0;

        simulation.setTotalPriceHt(Math.max(0.0, (simulation.getTotalPriceHt() != null ? simulation.getTotalPriceHt() : 0.0) - elementPrice));
        simulation.setTotalPriceTtc(Math.max(0.0, (simulation.getTotalPriceTtc() != null ? simulation.getTotalPriceTtc() : 0.0) - elementTotalPrice));
        simulation.setTotalQuantity(Math.max(0, (simulation.getTotalQuantity() != null ? simulation.getTotalQuantity() : 0) - elementQuantity));

        simulationRepository.save(simulation);
    }

    private void buildSimulationActor(SimulationActor simulationActor, Simulation simulation, Actor actor) {
        ActorRole actorRole = actor.getActorRole().stream()
                .filter(role -> "FOURN".equals(role.getRole().getCode()))
                .findFirst()
                .orElseThrow(() -> new ResourcesNotFoundException("Supplier does not have the FOURN role", MODULE));
        simulationActor.setActor(actor);
        simulationActor.setRole(actorRole.getRole());
        simulationActor.setSimulation(simulation);
    }

    private void buildVehicleProperties(Vehicle vehicle, VehicleRequestDto vehicleRequestDto, TotalPriceCalculationResultDto totalPriceCalculationResultDto, Actor actor, StaticParameter nature) {
        TaxRate taxRate = taxeRateRepository.findByCodeAndDeletedAtIsNull(totalPriceCalculationResultDto.getTaxCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Tax rate not found or inactive.", MODULE));
        Tax tax = taxRate.getTax();

        Address address = actor.getAddresses().stream().findFirst().orElse(null);//TODO: check if this is correct
        Category category = categoryRepository.findById(609L).orElse(null); //TODO: check if this is correct
        Currency currency = currencyRepository.findById(1469L).orElse(null);//TODO: check if this is correct
        vehicle.setDescription(""); // TODO: check if this is correct
        vehicle.setRegistration(""); // TODO: check if this is correct
        vehicle.setLabel(vehicle.getBrand() + " - " + vehicle.getModel()); // TODO: check if this is correct

        vehicle.setCondition(vehicleRequestDto.getCondition());
        vehicle.setPrice(totalPriceCalculationResultDto.getPrice());
        vehicle.setTotalPrice(totalPriceCalculationResultDto.getTotalPrice());
        vehicle.setTax(tax);
        vehicle.setTaxRate(taxRate);
        vehicle.setCategory(category);
        vehicle.setCurrency(currency);
        vehicle.setSupplier(actor);
        vehicle.setNature(nature);
        vehicle.setBrand(vehicleRequestDto.getBrand());
        vehicle.setModel(vehicleRequestDto.getModel());
        vehicle.setStyle(vehicleRequestDto.getStyle());
        vehicle.setFinition(vehicleRequestDto.getFinition());
        vehicle.setMotor(vehicleRequestDto.getMotor());
        vehicle.setTypeGender(vehicleRequestDto.getTypeGender());
        vehicle.setProviderAddress(address);
        vehicle.setSupplierAddressAssignmentDate(LocalDate.now());
        vehicle.setMileage(vehicleRequestDto.getMileage());
    }

    private void buildMaterialProperties(Material material, MaterialRequestDto dto) {

       Market market = marketRepository.findByCode(dto.getMarketCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Market not found", MODULE));

        material.setTypeEquipment(equipmentRepository.findByCode(dto.getTypeEquipment())
                .orElseThrow(() -> new ResourcesNotFoundException("Equipment type not found", MODULE)));
        material.setMarket(market);
        material.setFinancingAmount(dto.getFinancingAmount());
    }

    private void buildSimulationProperties(Simulation simulation, StaticParameter nature, Actor business, Activity activity, Product product, Offer offer) {
        simulation.setNature(nature);
        simulation.setBusinessReference(business);
        simulation.setActivity(activity);
        simulation.setProduct(product);
        simulation.setOffer(offer);
        simulation.setStatus(ASSET_STATUS);
        simulation.setTitle("Simulation 1");  // TODO: check if this is correct
    }

    private void buildPriceSimulationVehicle(Simulation simulation, TotalPriceCalculationResultDto totalPriceCalculationResultDto) {
        simulation.setTotalQuantity(totalPriceCalculationResultDto.getQuantity());
        simulation.setTotalPriceHt(totalPriceCalculationResultDto.getTotalPriceHt());
        simulation.setTotalPriceTtc(totalPriceCalculationResultDto.getTotalPriceTtc());
    }

    private void buildPriceSimulationMaterial(Simulation simulation, Material material, MaterialRequestDto materialRequestDto, boolean isNew) {
        double currentPriceHt = simulation.getTotalPriceHt() != null ? simulation.getTotalPriceHt() : 0.0;
        double currentPriceTtc = simulation.getTotalPriceTtc() != null ? simulation.getTotalPriceTtc() : 0.0;
        int currentQuantity = simulation.getTotalQuantity() != null ? simulation.getTotalQuantity() : 0;
        // check if the simulation is new or existing
        if (simulation.getId() == null) {
            simulation.setTotalQuantity(1);
            simulation.setTotalPriceHt(material.getPrice());
            simulation.setTotalPriceTtc(material.getTotalPrice());
        } else {
            // check if the simulationElement is new or existing
            if (isNew) {
                simulation.setTotalQuantity(1 + currentQuantity);
                simulation.setTotalPriceHt(currentPriceHt + material.getPrice());
                simulation.setTotalPriceTtc(currentPriceTtc + material.getTotalPrice());
            } else {
                SimulationElement simulationElement = simulationElementRepository
                        .findByIdAndDeletedAtIsNull(materialRequestDto.getSimulationElementId())
                        .orElseThrow(() -> new ResourcesNotFoundException("This simulation is not associated to any element", MODULE));
                // update the simulation with the new material price (remove the old material price and add the new one)
                simulation.setTotalQuantity(currentQuantity);
                simulation.setTotalPriceHt(currentPriceHt - simulationElement.getElement().getPrice() + material.getPrice());
                simulation.setTotalPriceTtc(currentPriceTtc - simulationElement.getElement().getTotalPrice() + material.getTotalPrice());
            }
        }
    }

    private void buildSimulationElementMaterial(SimulationElement simulationElement, Material material) {
        simulationElement.setQuantity(1);  // TODO: check if this is correct
        simulationElement.setDiscount(0.0); // TODO: check if this is correct
        simulationElement.setDiscountAmountHt(material.getPrice()); // TODO: check if this is correct
        simulationElement.setDiscountAmountTtc(material.getTotalPrice()); // TODO: check if this is correct
    }

    private void buildSimulationElementVehicle(SimulationElement simulationElement, VehicleRequestDto vehicleRequestDto, TotalPriceCalculationResultDto totalPriceCalculationResultDto, Vehicle vehicle) {
        simulationElement.setDiscount(vehicleRequestDto.getDiscount());
        simulationElement.setQuantity(vehicleRequestDto.getQuantity());
        simulationElement.setDiscountAmountHt(totalPriceCalculationResultDto.getDiscountAmountHt());
        simulationElement.setDiscountAmountTtc(totalPriceCalculationResultDto.getDiscountAmountTtc());
        simulationElement.setElement(vehicle);
    }



    private TotalPriceCalculationResultDto calculateTotalPricesVehicle(VehicleRequestDto vehicleRequestDto) {
        VehicleStock vehicleStock = getVehicleStockDetails(vehicleRequestDto);
        // TODO : check if this is correct : add tax rate and price ht , and tax value
        /*
        TaxRate taxRate = taxeRateRepository.findByCode(vehicleStock.getTaxRate().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Tax rate not found", MODULE));*/
        // Calcul du prix total après application de la taxe
        //Double price = vehicleStock.getPrice();
        //double taxRateCalculated = taxRate.getRate() / 100.0;
        //Double totalPriceCalculated = price * (1 + taxRateCalculated);

        // Other calculations
        Double discount = vehicleRequestDto.getDiscount();
        int quantity = (vehicleRequestDto.getQuantity() == null || vehicleRequestDto.getQuantity() == 0) ? 1 : vehicleRequestDto.getQuantity();
        Double discountAmountHt = discount * vehicleStock.getTotalPrice() / 100;
        Double discountAmountTtc = discount * vehicleStock.getTotalPrice() / 100;
        Double totalPriceHt = (vehicleStock.getTotalPrice() - discountAmountHt) * quantity;
        Double totalPriceTtc = (vehicleStock.getTotalPrice() - discountAmountTtc) * quantity;

        // Populate the result DTO
        TotalPriceCalculationResultDto result = new TotalPriceCalculationResultDto();
        result.setTotalPrice(vehicleStock.getTotalPrice());
        result.setPrice(vehicleStock.getTotalPrice());
        result.setTaxCode("EXOSU");  // TODO : check if this is correct
        result.setDiscountAmountHt(discountAmountHt);
        result.setDiscountAmountTtc(discountAmountTtc);
        result.setTotalPriceHt(totalPriceHt);
        result.setTotalPriceTtc(totalPriceTtc);
        result.setQuantity(quantity);
        return result;
    }

    private SimulationResponseDto buildSimulationRequestDto(Simulation simulation, List<SimulationElement> simulationElements, Offer offer) {
        SimulationDto simulationDto = modelMapper.map(simulation, SimulationDto.class);
        OfferResponseDto offerDto = modelMapper.map(offer, OfferResponseDto.class);

        List<RelatedSimulationInfo> simulationsTitle =
                customSimulationRepository.findTitlesByOfferIdAndDeletedAtIsNull(simulation.getOffer().getId());

        List<SimulationElementDto> simulationElementDtoList = new ArrayList<>();

        for (SimulationElement simulationElement : simulationElements) {
            SimulationElementDto simulationElementDto = modelMapper.map(simulationElement, SimulationElementDto.class);
            if (simulationElement.getElement() != null) {
                ElementDto elementDto = modelMapper.map(simulationElement.getElement(), ElementDto.class);
                if ("VH".equals(elementDto.getNature().getCode())) {
                    Vehicle vehicle = vehicleRepository.findById(elementDto.getId())
                            .orElseThrow(() -> new ResourcesNotFoundException("No vehicle found with element ID", MODULE));
                    elementDto = modelMapper.map(vehicle, ElementDto.class);
                } else {
                    Material material = materialRepository.findById(elementDto.getId())
                            .orElseThrow(() -> new ResourcesNotFoundException("No material found with element ID", MODULE));
                    elementDto = modelMapper.map(material, ElementDto.class);
                }
                simulationElementDto.setElement(elementDto);
            }
            simulationElementDtoList.add(simulationElementDto);
        }
        simulationDto.setSimulationElementDto(simulationElementDtoList);

        SimulationResponseDto simulationResponseDto = new SimulationResponseDto();
        simulationResponseDto.setOffer(offerDto);
        simulationResponseDto.getOffer().setRelatedSimulationInfoList(simulationsTitle);
        simulationResponseDto.setSimulationDto(simulationDto);

        return simulationResponseDto;
    }

    private Offer getOrCreateOffer(String offerReference) {
        return offerRepository.findByReferenceAndDeletedAtIsNull(offerReference)
                .orElseGet(() -> {
                    Offer newOffer = new Offer();

                    Milestone optionalMilestone = milestoneRepository.findByCode("OUVERT")
                            .orElseThrow(() -> new ResourcesNotFoundException("No milestone found for code 'OUVERT'", MODULE));

                    Phase optionalPhase = phaseRepository.findByCodeAndAssociatedTo("INI", "DOSSIER")
                            .orElseThrow(() -> new ResourcesNotFoundException("No phase found for code 'INI' and associated to 'Dossier'", MODULE));

                    newOffer.setReference(OFFER_REF_PREFIX + createReference());
                    newOffer.setPhaseCode(optionalPhase);
                    newOffer.setMilestoneCode(optionalMilestone);

                    return newOffer;
                });
    }

    private VehicleStock getVehicleStockDetails(VehicleRequestDto vehicleRequestDto) {
        // Find first vehicle stock by brand, model, style, motor, and finition
        // We need to add color and other parameters to the search to be more precise and unique
        return vehicleStockRepository.findFirstByBrandAndModelAndStyleAndMotorAndFinition(
                        vehicleRequestDto.getBrand(),
                        vehicleRequestDto.getModel(),
                        vehicleRequestDto.getStyle(),
                        vehicleRequestDto.getMotor(),
                        vehicleRequestDto.getFinition())
                .orElseThrow(() -> new ResourcesNotFoundException("No resource found for this vehicle", "offer"));
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteOffer(Long offerId) {
        Date deletedAt = new Date();

        Offer offer = offerRepository.findByIdAndDeletedAtIsNull(offerId)
                .orElseThrow(() -> new ResourcesNotFoundException(OFFER_NOT_FOUND, MODULE));

        List<Simulation> simulations = simulationRepository.findAllByOffer_ReferenceAndDeletedAtIsNull(offer.getReference());

        for (Simulation simulation : simulations) {
            List<Rental> rentals = rentalRepository.findBySimulationActorIdSimulationIdAndDeletedAtIsNullOrderByCreatedAtDesc(simulation.getId());
            for (Rental rental : rentals) {
                rental.setDeletedAt(deletedAt);
                rentalRepository.save(rental);
            }

            List<SimulationActor> simulationActors = simulationActorRepository.findAllBySimulationIdAndDeletedAtIsNull(simulation.getId());
            for (SimulationActor simulationActor : simulationActors) {
                simulationActor.setDeletedAt(deletedAt);
                simulationActorRepository.save(simulationActor);
            }

            List<SimulationElement> simulationElements = simulationElementRepository.findAllBySimulationIdAndDeletedAtIsNull(simulation.getId());
            for (SimulationElement simulationElement : simulationElements) {
                simulationElement.setDeletedAt(deletedAt);
                simulationElementRepository.save(simulationElement);
            }

            for (SimulationElement simulationElement : simulationElements) {
                Element element = simulationElement.getElement();
                if (element != null && element.getNature() != null && "VH".equals(element.getNature().getCode())) {
                    element.setDeletedAt(deletedAt);
                    elementRepository.save(element);
                }
            }

            simulation.setDeletedAt(deletedAt);
            simulationRepository.save(simulation);
        }

        offer.setDeletedAt(deletedAt);
        offerRepository.save(offer);

        return ResponseEntity.ok(new InformativeMessage("Offer and all associated simulations, simulation elements, and elements deleted successfully"));
    }



    private void validateSimulation(MaterialRequestDto materialRequestDto, Simulation simulation) {

        Product product = productRepository.findByCode(materialRequestDto.getProductCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Product not found", MODULE));
        if (!product.getId().equals(simulation.getProduct().getId())) {
            throw new BusinessException("The product and simulation are not related", "0");
        }

        Actor business = actorRepository.findByReferenceAndTypeAndDeletedAtIsNull(
                        materialRequestDto.getBusinessReference(), ActorTypes.MANAGEMENT_COMPANY)
                .orElseThrow(() -> new ResourcesNotFoundException("Business not found", MODULE));
        if (!business.getId().equals(simulation.getBusinessReference().getId())) {
            throw new BusinessException("The business and simulation are not related", "0");
        }
        Activity activity = activityRepository.findByCode(materialRequestDto.getActivityCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Activity not found", MODULE));
        if (!activity.getCode().equals(simulation.getActivity().getCode())) {
            throw new BusinessException("The activity and simulation are not related", "0");
        }
    }

    @Transactional
    protected void associateDefaultPaymentMethods(SimulationActor simulationActor, Actor actor) {
        List<SettlementMeans> settlementMeansList = settlementMeansRepository.findByActorIdAndDeletedAtIsNull(actor.getId());
        for (SettlementMeans settlementMeans : settlementMeansList) {
            PaymentMethod paymentMethod = settlementMeans.getPaymentMethod();
            if (paymentMethod != null) {
                SimulationActorPayments simulationActorPayments = new SimulationActorPayments();
                simulationActorPayments.setSimulationActor(simulationActor);
                simulationActorPayments.setPaymentMethod(paymentMethod);
                simulationActorPayments.setType(settlementMeans.getPaymentType());
                simulationActorPayments.setStartDate(LocalDate.now()); // TODO : check if this is correct start date
                simulationActorPaymentsRepository.save(simulationActorPayments);
            }
        }
    }
}

