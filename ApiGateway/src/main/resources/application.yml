spring:
  application:
    name: Gateway
  services:
    auth:
      url: http://auth:8810/api/v1/
  cloud:
    loadbalancer:
      ribbon:
        enabled: true
    gateway:
      globalcors:
        corsConfigurations:
          "[/**]":
            allowedMethods:
              - GET
              - POST
              - PUT
              - DELETE
              - PATCH
              - OPTIONS
            allowedHeaders:
              - Authorization
              - Content-Type
              - Cache-Control
              - X-Requested-With
              - accept
              - Origin
              - Access-Control-Request-Method
              - Access-Control-Request-Headers
              - X-Forwarded-*
              - Sec-WebSocket-Extensions
              - Sec-WebSocket-Key
              - Sec-WebSocket-Version
              - x-api-key
            allowCredentials: true
            exposedHeaders:
              - "Sec-WebSocket-Accept"
      routes:
        - id: Actor
          uri: http://actor:8802
          predicates:
            - Path=/api/v1/actors/**, /api/v1/roles/**, /api/v1/bank-account/**, /api/v1/iban/**, /api/v1/business-payment/**, /api/v1/contacts/**, /api/v1/parties/**, /api/v1/businesses/**
          filters:
            - AuthenticationFilter
        - id: Offer
          uri: http://offer:8803
          predicates:
            - Path=/api/v1/offers/**, /api/v1/simulations/**, /api/v1/static/**, /api/v1/scales/**
          filters:
            - AuthenticationFilter
        - id: Asset
          uri: http://asset:8805
          predicates:
            - Path=/api/v1/assets/**
          filters:
            - AuthenticationFilter
        - id: auth
          uri: http://auth:8810
          predicates:
            - Path=/api/v1/auth/**
          filters:
            - AuthenticationFilter
        - id: engine
          uri: http://engine-fo:8806
          predicates:
            - Path=/api/v1/finance-calculator/**
          filters:
            - AuthenticationFilter
        - id: Mailing
          uri: http://mail-sender:8092
          predicates:
            - Path=/api/v1/alert/**
          filters:
            - AuthenticationFilter
        - id: FileSystem
          uri: http://file-system:8813
          predicates:
            - Path=/api/v1/file-system/**
          filters:
            - AuthenticationFilter
#Swagger routes:
        - id: ActorDocumentation
          uri: http://actor:8802
          predicates:
            - Path=/swagger/actors/**
          filters:
            - RewritePath=/swagger/actors/(?<segment>.*), /$\{segment}
        - id: AssetDocumentation
          uri: http://asset:8805
          predicates:
            - Path=/swagger/assets/**
          filters:
            - RewritePath=/swagger/assets/(?<segment>.*), /$\{segment}
        - id: OfferDocumentation
          uri: http://offer:8803
          predicates:
            - Path=/swagger/offers/**
          filters:
            - RewritePath=/swagger/offers/(?<segment>.*), /$\{segment}
        - id: AuthDocumentation
          uri: http://auth:8810
          predicates:
            - Path=/swagger/auth/**
          filters:
            - RewritePath=/swagger/auth/(?<segment>.*), /$\{segment}
        - id: EngineDocumentation
          uri: http://engine-fo:8806
          predicates:
            - Path=/swagger/finance-calculator/**
          filters:
            - RewritePath=/swagger/finance-calculator/(?<segment>.*), /$\{segment}
        - id: FileSystemDocumentation
          uri: http://file-system:8813
          predicates:
            - Path=/swagger/file-system/**
          filters:
            - RewritePath=/swagger/file-system/(?<segment>.*), /$\{segment}
springdoc:
  enable-native-support: true
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    urls:
      - name: Actor Service
        url: /swagger/actors/v3/api-docs
      - name: Asset Service
        url: /swagger/assets/v3/api-docs
      - name: Offre Service
        url: /swagger/offers/v3/api-docs
      - name: Auth Service
        url: /swagger/auth/v3/api-docs
      - name: Engine Service
        url: /swagger/finance-calculator/v3/api-docs
      - name: File System Service
        url: /swagger/file-system/v3/api-docs

server:
  port: 8800
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    routes:
      enabled: true
jwt:
  secret-key: MIHcAgEBBEIB9D1/yfXxYbvgarUXve1XWs0RUJQW15N3//wr8HSAtNe0sCP/PEQOG1udSMPccdNL1jjB7ep3Xw2GwTum0xqJIAGgBwYFK4EEACOhgYkDgYYABAAQzFHMqFC30I7wTh53nugc4vusR9UXJGbpApE5l+C0aRSJ671vXUD1eaiBylRoUOcBV9I0+le/fq5RjyT6fTWYjQHkk9dU06wmo7yXDqngCWizRULxAKLZFw20MJg7ODfNW1GzTaZMd/T7ehJ6RIFXhsDJ058kJ7oimDutCwsHboAgTw==

# local profile configuration
---
spring:
  config:
    activate:
      on-profile: default
  services:
    auth:
      url: http://localhost:8710/api/v1/
  cloud:
    gateway:
      globalcors:
        corsConfigurations:
          "[/**]":
            allowedOriginPatterns:
              - "http://localhost:*"
      routes:
        - id: Actor
          uri: http://localhost:8702
          predicates:
            - Path=/api/v1/actors/**, /api/v1/roles/**, /api/v1/bank-account/**, /api/v1/iban/**, /api/v1/business-payment/**, /api/v1/contacts/**, /api/v1/parties/**, /api/v1/businesses/**
          filters:
            - AuthenticationFilter
        - id: Offer
          uri: http://localhost:8703
          predicates:
            - Path=/api/v1/offers/**, /api/v1/simulations/**, /api/v1/static/**, /api/v1/scales/**
          filters:
            - AuthenticationFilter
        - id: Asset
          uri: http://localhost:8705
          predicates:
            - Path=/api/v1/assets/**
          filters:
            - AuthenticationFilter
        - id: auth
          uri: http://localhost:8810
          predicates:
            - Path=/api/v1/auth/**
          filters:
            - AuthenticationFilter
        - id: engine
          uri: http://localhost:8806
          predicates:
            - Path=/api/v1/finance-calculator/**
          filters:
            - AuthenticationFilter
        - id: static-tables
          uri: http://localhost:8807
          predicates:
            - Path=/api/v1/static-tables/**
          filters:
            - AuthenticationFilter
        - id: Mailing
          uri: http://localhost:8092
          predicates:
            - Path=/api/v1/alert/**
          filters:
            - AuthenticationFilter
        - id: FileSystem
          uri: http://localhost:8813
          predicates:
            - Path=/api/v1/file-system/**
          filters:
            - AuthenticationFilter
        #Swagger routes:
        - id: ActorDocumentation
          uri: http://localhost:8702
          predicates:
            - Path=/swagger/actors/**
          filters:
            - RewritePath=/swagger/actors/(?<segment>.*), /$\{segment}
        - id: AssetDocumentation
          uri: http://localhost:8705
          predicates:
            - Path=/swagger/assets/**
          filters:
            - RewritePath=/swagger/assets/(?<segment>.*), /$\{segment}
        - id: OfferDocumentation
          uri: http://localhost:8703
          predicates:
            - Path=/swagger/offers/**
          filters:
            - RewritePath=/swagger/offers/(?<segment>.*), /$\{segment}
        - id: AuthDocumentation
          uri: http://localhost:8710
          predicates:
            - Path=/swagger/auth/**
          filters:
            - RewritePath=/swagger/auth/(?<segment>.*), /$\{segment}
        - id: EngineDocumentation
          uri: http://localhost:8806
          predicates:
            - Path=/swagger/finance-calculator/**
          filters:
            - RewritePath=/swagger/finance-calculator/(?<segment>.*), /$\{segment}
        - id: FileSystemDocumentation
          uri: http://localhost:8813
          predicates:
            - Path=/swagger/file-system/**
          filters:
            - RewritePath=/swagger/file-system/(?<segment>.*), /$\{segment}
server:
  port: 8700
logging:
  level:
    root: info
    com.netflix.zuul: info
    org.springframework.cloud.netflix: INFO
    org.springframework.web.socket: INFO
    org.springframework.cloud.gateway: INFO
    reactor.netty: INFO

# DEV profile configuration
---
spring:
  config:
    activate:
      on-profile: dev
  cloud:
    gateway:
      globalcors:
        corsConfigurations:
          "[/**]":
            allowedOriginPatterns:
              - "http://localhost:*"
              - "https://dev-*.data-tricks.net"
              - "https://dev-fo.kyra-app.com"
              - "https://dev-settings.kyra-app.com"
              - "https://wp-*-frontend.kyra-fo-app.com"
              - "https://da-*-frontend.kyra-fo-app.com"
              - "http://swm-mgr01.local.lan:*"
logging:
  level:
    root: info
    com.netflix.zuul: info
    org.springframework.cloud.netflix: info

# Test profile configuration
---
spring:
  config:
    activate:
      on-profile: test
  cloud:
    gateway:
      globalcors:
        corsConfigurations:
          "[/**]":
            allowedOriginPatterns:
              - "http://localhost:*"
              - "https://test-*.data-tricks.net"
              - "https://test-settings.kyra-app.com"
              - "https://test-fo.kyra-app.com"
logging:
  level:
    root: info
    com.netflix.zuul: info
    org.springframework.cloud.netflix: info

# STAGING profile configuration
---
spring:
  config:
    activate:
      on-profile: staging
  cloud:
    gateway:
      globalcors:
        corsConfigurations:
          "[/**]":
            allowedOriginPatterns:
              - "https://staging-fo.kyra-app.com"
logging:
  level:
    root: info
    com.netflix.zuul: info
    org.springframework.cloud.netflix: info
