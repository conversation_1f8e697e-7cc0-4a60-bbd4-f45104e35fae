package com.datatricks.assets.service;

import com.datatricks.common.exception.ConflictException;
import com.datatricks.common.exception.ResourcesNotFoundException;
import com.datatricks.common.exception.handler.InformativeMessage;
import com.datatricks.common.model.*;
import com.datatricks.common.model.dto.*;
import com.datatricks.common.repository.*;
import com.datatricks.common.utils.JpaQueryFilters;
import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@AllArgsConstructor
public class ElementService {

    private final ElementRepository elementRepository;
    private final AddressRepository addressRepository;
    private final CategoryRepository categoryRepository;
    private final ModelMapper modelMapper;
    private final PhaseRepository phaseRepository;
    private final MilestoneRepository milestoneRepository;
    private final ActorRepository actorRepository;
    private final AmortizationService amortizationService;
    private final ShippingService shippingService;
    private final SimulationElementRepository simulationElementRepository;
    private final SimulationActorRepository simulationActorRepository;
    private final AmortizationRepository amortizationRepository;
    private final ShippingRepository shippingRepository;
    private final OrderRepository orderRepository;
    private final OrderService orderService;
    private final TaxeRateRepository taxRateRepository;
    private final CurrencyRepository currencyRepository;
    private final StaticParameterRepository staticParameterRepository;
    private final EquipmentRepository equipmentRepository;
    private static final String ADDRESS_NOT_BILLING = "The address provided is not a billing address";
    private static final String MODULE = "ELEMENT";
    private static final String ELEMENT_NOT_FOUND = "Element not found";
    private static final String ELEMENT = "ELEMENT";
    private static final String ACTOR = "ACTOR";
    private static final String ASSET_NOT_FOUND = "Asset not found";
    private static final String ASSET = "ASSET";
    private static final String CLIENT_ADDRESS_CONFLICT = "The address provided does not belong to the client";
    private static final String PROVIDER_ADDRESS_CONFLICT = "The address provided does not belong to the provider";
    private static final String ADDRESS_NOT_FOUND = "Address not found";
    private static final String CLIENT_NOT_FOUND = "Client not found";
    private static final String PROVIDER_NOT_FOUND = "Provider not found";
    private static final String ORDER_NOT_FOUND = "Order not found";
    private static final String SIMULATION_NOT_FOUND = "Simulation not found";
    private static final String SIMULATION = "SIMULATION";
    private static final String ASSOCIATED_TO_NATURE = "NATURE";
    private final FileSystemService fileSystemService;


    @Transactional
    public ResponseEntity<SingleResultDto<ElementDto>> createElement(NewElementDto newElement) {
        Element element = "VH".equals(newElement.getNatureCode())
                ? new Vehicle()
                : "MT".equals(newElement.getNatureCode())
                ? new Material()
                : null;

        if (element == null) {
            throw new IllegalArgumentException("Invalid nature code: " + newElement.getNatureCode());
        }

        List<Address> providerPossibleAddresses = addressRepository
                .findByActorReferenceAndDeletedAtIsNull(newElement.getSupplierReference()).stream()
                .filter(Address::getIsBilling)
                .toList();

        if (!providerPossibleAddresses.isEmpty()) {
            newElement.setProviderAddressReference(providerPossibleAddresses.getFirst().getReference());
        }

        buildElement(newElement, element);
        element.setCreatedAt(new Date());
        Element savedElement = elementRepository.saveAndFlush(element);
        ElementDto elementDto = this.modelMapper.map(savedElement, ElementDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementDto>builder().data(elementDto).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementDto>> updateElement(Long elementId, NewElementDto element) {
        Element existingElement = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElse(null);
        if (existingElement != null) {
            buildElement(element, existingElement);
            existingElement.setModifiedAt(new Date());

            Element updatedElement = elementRepository.saveAndFlush(existingElement);

            /* send message to kafka
            transactionSynchronizationUtil.executeAfterCommit(() -> {
                assetProducer.sendElementMessage(updatedElement, OperationType.PUT, MODULE);
            });*/

            ElementDto elementDto = this.modelMapper.map(updatedElement, ElementDto.class);
            return ResponseEntity.ok(SingleResultDto.<ElementDto>builder().data(elementDto).build());
        } else {
            throw new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE);
        }
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteElement(Long elementId) {
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId)
                .orElseThrow(() -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        element.setDeletedAt(new Date());
        element.getAmortization().setDeletedAt(new Date());
        elementRepository.saveAndFlush(element);

        /* send message to kafka
        transactionSynchronizationUtil.executeAfterCommit(() -> {
            assetProducer.sendElementMessage(element, OperationType.DELETE, MODULE);
        });*/
        return ResponseEntity.ok(new InformativeMessage("Resource with ID " + elementId + " has been deleted successfully"));
    }

    @Transactional
    public ResponseEntity<PageDto<ElementDto>> getElements(Map<String, String> params) {
        JpaQueryFilters<Element> filters = new JpaQueryFilters<>(params, Element.class);
        Page<Element> page = elementRepository.findAll(filters.getSpecification(), filters.getPageable());

        List<ElementDto> filteredElements = page.stream()
                .map(element -> {
                    ElementDto elementDto = this.modelMapper.map(element, ElementDto.class);
                    if (element.getImageName() != null && !element.getImageName().isBlank()) {
                        elementDto.setImage(fetchFileResult(element.getImageName()));
                    }
                    return elementDto;
                })
                .toList();

        return ResponseEntity.ok(PageDto.<ElementDto>builder()
                .data(filteredElements)
                .total(page.getTotalElements())
                .build());
    }


    @Transactional
    public ResponseEntity<SingleResultDto<ElementDto>> getElementById(Long elementId) {
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId)
                .orElseThrow(() -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));

        ElementDto elementDto = this.modelMapper.map(element, ElementDto.class);
        if (element.getImageName() != null && !element.getImageName().isBlank()) {
            elementDto.setImage(fetchFileResult(element.getImageName()));
        }
        return ResponseEntity.ok(SingleResultDto.<ElementDto>builder()
                .data(elementDto)
                .build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementAmortizationDto>> createOrUpdateElementAmortization(
            Long elementId,
            NewElementAmortizationDto newElementAmortizationDto) {
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        buildElementAmortization(newElementAmortizationDto, element);
        element.setModifiedAt(new Date());
        Element updatedElement = elementRepository.saveAndFlush(element);

        ElementAmortizationDto elementAmortization = this.modelMapper.map(updatedElement, ElementAmortizationDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementAmortizationDto>builder().data(elementAmortization).build());
    }


    @Transactional
    public ResponseEntity<SingleResultDto<ElementBaseDto>> updateElementBase(
            Long elementId,
            NewElementBaseDto newElementBaseDto) {
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        NewElementDto newElementDto = modelMapper.map(newElementBaseDto, NewElementDto.class);
        buildBaseElement(newElementDto, element);
        element.setModifiedAt(new Date());
        Element updatedElement = elementRepository.saveAndFlush(element);

        ElementBaseDto elementBase = this.modelMapper.map(updatedElement, ElementBaseDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementBaseDto>builder().data(elementBase).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementPaymentDto>> updateElementPayment(
            Long elementId,
            NewElementPaymentDto newElementPaymentDto) {

        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        NewElementDto newElementDto = modelMapper.map(newElementPaymentDto, NewElementDto.class);
        buildElementPayment(newElementDto, element);
        element.setModifiedAt(new Date());
        Element updatedElement = elementRepository.saveAndFlush(element);

        ElementPaymentDto elementPaymentDto = this.modelMapper.map(updatedElement, ElementPaymentDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementPaymentDto>builder().data(elementPaymentDto).build());
    }

    private void buildNewElement(NewElementDto newElement, Element element) {


        Category category = categoryRepository.findFirstByCode(newElement.getCategoryCode()).orElseThrow(
                () -> new ResourcesNotFoundException("Category not found", "CATEGORY", MODULE));
        Actor supplier = actorRepository.findByReferenceAndDeletedAtIsNull(newElement.getSupplierReference()).orElseThrow(
                () -> new ResourcesNotFoundException("Supplier not found", "SUPPLIER", MODULE));


        StaticParameter nature =
                staticParameterRepository.findByCodeAndAssociatedTo(newElement
                                .getNatureCode(), ASSOCIATED_TO_NATURE)
                        .orElseThrow(() -> new ResourcesNotFoundException("Nature not found", "NATURE", MODULE));

        element.setNature(nature);
        element.setLabel(newElement.getLabel());
        element.setExternalReference(newElement.getExternalReference());
        element.setSupplier(Actor.builder().id(supplier.getId()).reference(supplier.getReference()).build());
        element.setCategory(category);
        element.setCondition(newElement.getCondition());
        element.setRegistration(newElement.getRegistration());
        element.setVin(newElement.getVin());
        element.setShortDescription(newElement.getShortDescription());
        element.setDescription(newElement.getDescription());
        element.setImageName(newElement.getImageName());
        // Set properties for Vehicle
        if ("VH".equals(newElement.getNatureCode())) {
            Vehicle vehicle = (Vehicle) element;
            vehicle.setBrand(newElement.getBrand());
            vehicle.setModel(newElement.getModel());
            vehicle.setStyle(newElement.getStyle());
            vehicle.setFinition(newElement.getFinition());
            vehicle.setMotor(newElement.getMotor());
        }
        // Set properties for Material
        else {
            Material material = (Material) element;
        }

    }


    private void buildBaseElement(NewElementDto newElement, Element element) {

        Category category = categoryRepository.findFirstByCode(newElement.getCategoryCode()).orElseThrow(
                () -> new ResourcesNotFoundException("Category not found", "CATEGORY", MODULE));

        Actor supplier = actorRepository.findByReferenceAndDeletedAtIsNull(newElement.getSupplierReference()).orElseThrow(
                () -> new ResourcesNotFoundException("Supplier not found", "SUPPLIER", MODULE));

        Phase phase = phaseRepository.findFirstByCode(newElement.getPhaseCode()).orElseThrow(
                () -> new ResourcesNotFoundException("Phase not found", "PHASE", MODULE));

        Milestone milestone = milestoneRepository.findFirstByCode(newElement.getMilestoneCode()).orElseThrow(
                () -> new ResourcesNotFoundException("Milestone not found", "MILESTONE", MODULE));

        element.setPhase(phase);
        element.setMilestone(milestone);
        element.setLabel(newElement.getLabel());
        element.setExternalReference(newElement.getExternalReference());
        element.setSupplier(supplier);
        element.setCategory(category);
        element.setCondition(newElement.getCondition());
        element.setRegistration(newElement.getRegistration());
        element.setVin(newElement.getVin());
        element.setShortDescription(newElement.getShortDescription());
        element.setDescription(newElement.getDescription());
        element.setStartDate(newElement.getStartDate());
        element.setEndDate(newElement.getEndDate());
        element.setImageName(newElement.getImageName());
    }


    private void buildElementAmortization(NewElementAmortizationDto newElementAmortizationDto, Element element) {
        if (newElementAmortizationDto.getAmortization() != null) {
            AmortizationDto amortizationDto = newElementAmortizationDto.getAmortization();
            Amortization amortization;

            if (amortizationDto.getId() != null) {
                // Mettre à jour l'amortissement existant
                amortizationService.updateAmortization(amortizationDto.getId(), amortizationDto);
                amortization = amortizationRepository.findByIdAndDeletedAtIsNull(amortizationDto.getId())
                        .orElseThrow(() -> new ResourcesNotFoundException("Amortization not found", "AMORTIZATION", MODULE));
            } else {
                // Créer un nouvel amortissement
                ResponseEntity<SingleResultDto<AmortizationDto>> response = amortizationService.createAmortization(amortizationDto);
                AmortizationDto createdAmortizationDto = Objects.requireNonNull(response.getBody()).getData();

                amortization = amortizationRepository.findByIdAndDeletedAtIsNull(createdAmortizationDto.getId())
                        .orElseThrow(() -> new ResourcesNotFoundException("Amortization not found", "AMORTIZATION", MODULE));
            }

            // Associer l'amortissement à l'élément
            element.setAmortization(amortization);
        }
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementProviderAddressDto>> updateElementProviderAddress(
            Long elementId,
            NewElementProviderAddressDto newElementProviderAddressDto) {

        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        NewElementDto newElementDto = modelMapper.map(newElementProviderAddressDto, NewElementDto.class);
        buildElementProviderAddress(newElementDto, element);
        element.setModifiedAt(new Date());
        Element updatedElement = elementRepository.saveAndFlush(element);

        ElementProviderAddressDto elementAddress = this.modelMapper.map(updatedElement, ElementProviderAddressDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementProviderAddressDto>builder().data(elementAddress).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementClientAddressDto>> updateElementClientAddress(
            Long elementId,
            NewElementClientAddressDto newElementClientAddressDto) {
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        NewElementDto newElementDto = modelMapper.map(newElementClientAddressDto, NewElementDto.class);
        buildElementClientAddress(newElementDto, element);
        element.setModifiedAt(new Date());
        Element updatedElement = elementRepository.saveAndFlush(element);
        // The assetId usage has been removed
        ElementClientAddressDto elementAddress = this.modelMapper.map(updatedElement, ElementClientAddressDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementClientAddressDto>builder().data(elementAddress).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementShippingDto>> createOrUpdateElementShipping(
            Long elementId,
            NewElementShippingDto newElementShippingDto) {
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));

        buildElementShipping(newElementShippingDto, element);
        element.setModifiedAt(new Date());
        Element updatedElement = elementRepository.saveAndFlush(element);

        ElementShippingDto elementShipping = modelMapper.map(updatedElement, ElementShippingDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementShippingDto>builder().data(elementShipping).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementExtraDto>> updateElementExtra(
            Long elementId,
            NewElementExtraDto newElementExtraDto) {
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));

        NewElementDto newElementDto = modelMapper.map(newElementExtraDto, NewElementDto.class);
        buildElementExtra(newElementDto, element);
        element.setModifiedAt(new Date());
        Element updatedElement = elementRepository.saveAndFlush(element);

        ElementExtraDto elementExtra = this.modelMapper.map(updatedElement, ElementExtraDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementExtraDto>builder().data(elementExtra).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementOrderDto>> createOrUpdateElementOrder(
            Long elementId,
            ElementOrderDto newElementOrderDto) {

        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));

        NewElementDto newElementDto = modelMapper.map(newElementOrderDto, NewElementDto.class);

        buildElementOrder(newElementDto, element);

        element.setModifiedAt(new Date());

        Element updatedElement = elementRepository.saveAndFlush(element);

        ElementOrderDto elementOrder = modelMapper.map(updatedElement, ElementOrderDto.class);

        return ResponseEntity.ok(SingleResultDto.<ElementOrderDto>builder().data(elementOrder).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementOrderDto>> getElementOrderById(Long elementId) {
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId)
                .orElseThrow(() -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        ElementOrderDto elementOrderDto = this.modelMapper.map(element, ElementOrderDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementOrderDto>builder().data(elementOrderDto).build());
    }

    private void buildElementProviderAddress(NewElementDto newElement, Element element) {
        if (newElement.getProviderAddressReference() != null && !newElement.getProviderAddressReference().isBlank()) {
            if (element.getSupplier() == null) {
                throw new ResourcesNotFoundException(PROVIDER_NOT_FOUND, "PROVIDER_NOT_FOUND", MODULE);
            }

            List<Address> providerPossibleAddresses = addressRepository.findByActorReferenceAndDeletedAtIsNull(
                    element.getSupplier().getReference());
            if (providerPossibleAddresses.isEmpty()) {
                throw new ResourcesNotFoundException(ADDRESS_NOT_FOUND, "ADDRESS_NOT_FOUND", MODULE);
            }
            List<Address> providerAddress = providerPossibleAddresses.stream()
                    .filter(address -> address.getReference().equals(newElement.getProviderAddressReference()))
                    .toList();
            if (providerAddress.isEmpty()) {
                throw new ConflictException(PROVIDER_ADDRESS_CONFLICT, "PROVIDER_ADDRESS_CONFLICT", PROVIDER_ADDRESS_CONFLICT, MODULE);
            }
            Address billingAddress = providerAddress.stream()
                    .filter(Address::getIsBilling)
                    .findFirst()
                    .orElseThrow(() -> new ConflictException(ADDRESS_NOT_BILLING, "ADDRESS_NOT_BILLING", ADDRESS_NOT_BILLING, MODULE));

            element.setProviderAddress(billingAddress);
            element.setSupplierAddressAssignmentDate(newElement.getSupplierAddressAssignmentDate());
        }
    }

    private void buildElementClientAddress(NewElementDto newElement, Element element) {
        if (newElement.getClientAddressReference() != null && !newElement.getClientAddressReference().isBlank()) {
            Actor actor = element.getClient();

            if (actor == null) {
                throw new ResourcesNotFoundException(CLIENT_NOT_FOUND, "CLIENT_NOT_FOUND", MODULE);
            }

            List<Address> clientPossibleAddresses = addressRepository.findByActorReferenceAndDeletedAtIsNull(
                    actor.getReference());
            if (clientPossibleAddresses.isEmpty()) {
                throw new ResourcesNotFoundException(ADDRESS_NOT_FOUND, "ADDRESS_NOT_FOUND", MODULE);
            }

            List<Address> clientAddress = clientPossibleAddresses.stream()
                    .filter(address -> address.getReference().equals(newElement.getClientAddressReference()))
                    .toList();

            if (clientAddress.isEmpty()) {
                throw new ConflictException(CLIENT_ADDRESS_CONFLICT, "CLIENT_ADDRESS_CONFLICT", CLIENT_ADDRESS_CONFLICT, MODULE);
            }

            Address billingAddress = clientAddress.stream()
                    .filter(Address::getIsBilling)
                    .findFirst()
                    .orElseThrow(() -> new ConflictException(ADDRESS_NOT_BILLING, "ADDRESS_NOT_BILLING", ADDRESS_NOT_BILLING, MODULE));

            element.setClientAddress(billingAddress);
        }

        element.setCustomerAddressAssignmentDate(newElement.getCustomerAddressAssignmentDate());
    }


    private void buildElementShipping(NewElementShippingDto newElement, Element element) {
        if (newElement.getShipping() != null) {
            ShippingDto shippingDto = newElement.getShipping();
            Shipping shipping;

            if (shippingDto.getId() != null) {
                shippingService.updateShipping(shippingDto.getId(), shippingDto);
                shipping = shippingRepository.findByIdAndDeletedAtIsNull(shippingDto.getId())
                        .orElseThrow(() -> new ResourcesNotFoundException("Shipping not found", "SHIPPING", MODULE));
            } else {
                ResponseEntity<SingleResultDto<ShippingDto>> response = shippingService.createShipping(shippingDto);
                ShippingDto createdShippingDto = Objects.requireNonNull(response.getBody()).getData();

                shipping = shippingRepository.findByIdAndDeletedAtIsNull(createdShippingDto.getId())
                        .orElseThrow(() -> new ResourcesNotFoundException("Shipping not found", "SHIPPING", MODULE));
            }

            element.setShipping(shipping);
        }
    }

    private void buildElementOrder(NewElementDto newElement, Element element) {
        if (newElement.getOrder() != null) {
            OrderDto orderDto = newElement.getOrder();
            Order order;
            if (orderDto.getId() != null) {
                orderService.updateOrder(orderDto.getId(), orderDto);
                order = orderRepository.findByIdAndDeletedAtIsNull(orderDto.getId())
                        .orElseThrow(() -> new ResourcesNotFoundException(ORDER_NOT_FOUND, "ORDER", MODULE));
            } else {
                ResponseEntity<SingleResultDto<OrderDto>> response = orderService.createOrder(orderDto);
                OrderDto createdOrderDto = Objects.requireNonNull(response.getBody()).getData();

                order = orderRepository.findByIdAndDeletedAtIsNull(createdOrderDto.getId())
                        .orElseThrow(() -> new ResourcesNotFoundException(ORDER_NOT_FOUND, "ORDER", MODULE));
            }
            element.setOrder(order);
        }
    }

    private void buildElementExtra(NewElementDto newElement, Element element) {
        element.setLicense(newElement.getLicense());
    }

    private void buildElement(NewElementDto newElement, Element element) {
        buildNewElement(newElement, element);
        buildElementPayment(newElement, element);
        if (newElement.getClientAddressReference() != null) {
            buildElementClientAddress(newElement, element);
        }
        if (newElement.getProviderAddressReference() != null) {
            buildElementProviderAddress(newElement, element);
        }
        buildElementExtra(newElement, element);
    }

    private void buildElementPayment(NewElementDto newElement, Element element) {

        TaxRate taxRate = taxRateRepository.findByCodeAndDeletedAtIsNull(newElement.getTaxCode()).orElseThrow(
                () -> new ResourcesNotFoundException("Tax rate not found or inactive. Can you choose another one from the list", "TAX_RATE", MODULE));

        Double price = newElement.getPrice();
        double taxRateCalculated = taxRate.getRate() / 100.0;
        Double totalPriceCalculated = price * (1 + taxRateCalculated);
        Currency currency = currencyRepository.findFirstByCode(newElement.getCurrencyCode()).orElseThrow(
                () -> new ResourcesNotFoundException("Currency not found", "CURRENCY", MODULE));
        element.setPrice(price);
        element.setTotalPrice(totalPriceCalculated);
        element.setTax(taxRate.getTax());
        element.setTaxRate(taxRate);
        element.setCurrency(currency);
        element.setStartDate(newElement.getStartDate());
        element.setEndDate(newElement.getEndDate());
    }


    private FileResultDto fetchFileResult(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return null;
        }

        try {
            return fileSystemService.getFile(fileName)
                    .getBody()
                    .getData();
        } catch (Exception e) {
            return null;
        }
    }

}
