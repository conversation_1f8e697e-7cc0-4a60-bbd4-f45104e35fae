package com.datatricks.kafkamodule.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OperationNatureTypeMappingStreamDto {
    private Long id;

    @JsonProperty("operation_nature_type_mapping_code")
    private String operation_nature_type_code;

    @JsonProperty("operation_nature_Code")
    private String operation_nature_code;

    @JsonProperty("type_code")
    private String type_code;

    @JsonProperty("type_status")
    private Boolean type_status;
}
