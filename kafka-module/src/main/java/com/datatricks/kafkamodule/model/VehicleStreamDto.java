package com.datatricks.kafkamodule.model;

import lombok.*;

import java.util.UUID;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VehicleStreamDto {
    private Integer id;
    private UUID reference;
    private String brand_code;
    private String brand_label;
    private String model_group_code;
    private String model_group_label;
    private String body_code;
    private String body_label;
    private String model_code;
    private String model_label;
    private String doors_number;
    private String version_code;
    private String version_label;
    private String variant_code;
    private String variant_label;
    private String color_code;
    private String color_label;
    private String interior_code;
    private String interior_label;
    private String marketing_flag;
    private String energy_code;
    private String energy_label;
    private String type_code;
    private String type_label;
    private String power;
    private String class_code;
    private String class_label;
    private Integer vds_vehicle;
    private Double public_price_incl_tax;
    private String painting_payment_code;
    private Boolean status;
    private Boolean is_new;
}
