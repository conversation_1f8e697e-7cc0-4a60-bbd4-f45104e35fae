package com.datatricks.kafkamodule.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PatchActorActivityProductDTO {
    @Schema(description = "The activity code", example = "LOA")
    private String activityCode;

    @Schema(description = "The product code", example = "CBICBI")
    private String productCode;

    @Schema(description = "The activity status", example = "true")
    private boolean active;
}
