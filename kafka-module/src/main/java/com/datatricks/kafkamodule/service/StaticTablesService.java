package com.datatricks.kafkamodule.service;

import com.datatricks.common.exception.ResourcesNotFoundException;
import com.datatricks.common.exception.TechnicalException;
import com.datatricks.common.model.*;
import com.datatricks.common.repository.*;
import com.datatricks.kafkamodule.model.PatchActorActivityProductDTO;
import com.datatricks.kafkamodule.model.ScaleStreamDto;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.client.RestTemplate;

import java.util.Date;

@Service
@RequiredArgsConstructor
public class StaticTablesService {
    @Value("${spring.services.actor.url}")
    private String ACTOR_SERVER_URL;
    private final RestTemplate restTemplate;
    private static final String MODEL_NAME = "StaticTables";
    private static final String ERRORMESSAGE = "Error while processing message : ";
    private final MilestoneRepository milestoneRepository;
    private final PhaseRepository phaseRepository;
    private final LegalCategoryRepository legalCategoryRepository;
    private final BankBranchRepository bankBranchRepository;
    private final ServiceRepository serviceRepository;
    private final ServiceOperationRepository serviceOperationRepository;
    private final ServiceActivityProductRepository serviceActivityProductRepository;
    private final DelegationRepository delegationRepository;
    private final CurrencyRepository currencyRepository;
    private final ScaleServiceRepository scaleServiceRepository;
    private final ServiceNapRepository serviceNapRepository;
    private final ServiceActorRepository serviceActorRepository;
    private final CountryRepository countryRepository;
    private final PaymentMethodRepository paymentMethodRepository;
    private final ActivityRepository activityRepository;
    private final ProductRepository productRepository;
    private final RoleRepository roleRepository;
    private final LineTypeRepository lineTypeRepository;
    private final OperationRepository operationRepository;
    private final NatureRepository natureRepository;
    private final OperationNatureMappingRepository operationNatureMappingRepository;
    private final OperationNatureTypeMappingRepository operationNatureTypeMappingRepository;
    private final ProductLineTypeMappingRepository productLineTypeMappingRepository;
    private final StaticRoleRepository staticRoleRepository;
    private final TaxRepository taxRepository;
    private final TaxeRateRepository taxRateRepository;
    private final LegalActivitiesRepository legalActivitiesRepository;
    private final MarketRepository marketRepository;
    private final CategoryRepository categoryRepository;
    private final EquipmentRepository equipmentRepository;
    private final ApplicationCriteriaRepository applicationCriteriaRepository;
    private final ThirdPartyRepository thirdPartyRepository;
    private final ScaleElementVehicleVehicleRepository scaleElementVehicleVehicleRepository;
    private final ScaleElementVehicleRepository scaleElementVehicleRepository;
    private final ApplicationCriteriaActorsRepository applicationCriteriaActorsRepository;
    private final ScaleFinancialProductRepository scaleFinancialProductRepository;
    private final ScaleElementMaterialsRepository scaleElementMaterialsRepository;
    private final ScaleElementMaterialsEquipmentRepository scaleElementMaterialsEquipmentRepository;
    private final ScaleRepository scaleRepository;
    private final StaticParameterRepository staticParameterRepository;
    private final ActorRepository actorRepository;
    private final VehicleStockRepository vehicleStockRepository;
    private final PartnerRepository partnerRepository;
    private final ActorActivityProductRepository actorActivityProductRepository;

    @Transactional
    public void createNewMilestone(Milestone milestone, Acknowledgment acknowledgment) {
        try {
            milestoneRepository.save(milestone);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewPhase(Phase phase, Acknowledgment acknowledgment) {
        try {
            phaseRepository.save(phase);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewLegalCategory(LegalCategory legalCategory, Acknowledgment acknowledgment) {
        try {
            legalCategoryRepository.save(legalCategory);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewDelegation(Delegation delegation, Acknowledgment acknowledgment) {
        try {
            delegationRepository.save(delegation);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewCurrency(Currency currency, Acknowledgment acknowledgment) {
        try {
            currencyRepository.save(currency);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewCountry(Country country, Acknowledgment acknowledgment) {
        try {
            countryRepository.save(country);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewPaymentMethod(PaymentMethod paymentMethod, Acknowledgment acknowledgment) {
        try {
            paymentMethodRepository.save(paymentMethod);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewActivity(Activity activity, Acknowledgment acknowledgment) {
        try {
            activityRepository.save(activity);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewRole(Role role, Acknowledgment acknowledgment) {
        try {
            staticRoleRepository.findByCode(role.getStaticRoleCode().getCode()).ifPresentOrElse(
                    r -> {
                        role.setStaticRoleCode(r);
                        roleRepository.save(role);
                    },
                    () -> {
                        throw new ResourcesNotFoundException("AssociatedTo not found", "Role", role.getStaticRoleCode().getCode());
                    }
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateMilestone(Milestone milestone, Acknowledgment acknowledgment) {
        try {
            milestoneRepository.findByCode(milestone.getCode()).ifPresentOrElse(
                    m -> {
                        m.setLabel(milestone.getLabel());
                        m.setCode(milestone.getCode());
                        m.setActive(milestone.getActive());
                        m.setPhaseId(milestone.getPhaseId() != null ? milestone.getPhaseId() : m.getPhaseId());
                        milestoneRepository.save(m);
                    },
                    () -> milestoneRepository.save(milestone)
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updatePhase(Phase phase, Acknowledgment acknowledgment) {
        try {
            phaseRepository.findByCode(phase.getCode()).ifPresentOrElse(
                    p -> {
                        p.setLabel(phase.getLabel());
                        p.setAssociatedTo(phase.getAssociatedTo());
                        phaseRepository.save(p);
                    },
                    () -> phaseRepository.save(phase)
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateLegalCategory(LegalCategory legalCategory, Acknowledgment acknowledgment) {
        try {
            legalCategoryRepository.findByCode(legalCategory.getCode()).ifPresentOrElse(
                    lc -> {
                        lc.setLabel(legalCategory.getLabel());
                        legalCategoryRepository.save(lc);
                    },
                    () -> legalCategoryRepository.save(legalCategory)
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateDelegation(Delegation delegation, Acknowledgment acknowledgment) {
        try {
            delegationRepository.findByCode(delegation.getCode()).ifPresentOrElse(
                    d -> {
                        d.setLabel(delegation.getLabel());
                        d.setShortLabel(delegation.getShortLabel());
                        delegationRepository.save(d);
                    },
                    () -> delegationRepository.save(delegation)
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateCurrency(Currency currency, Acknowledgment acknowledgment) {
        try {
            currencyRepository.findByCode(currency.getCode()).ifPresentOrElse(
                    c -> {
                        c.setName(currency.getName());
                        c.setActive(currency.getActive());
                        currencyRepository.save(c);
                    },
                    () -> currencyRepository.save(currency)
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateCountry(Country country, Acknowledgment acknowledgment) {
        try {
            countryRepository.findByCode(country.getCode()).ifPresentOrElse(
                    c -> {
                        c.setLabel(country.getLabel());
                        c.setLanguage(country.getLanguage());
                        c.setCountryTaxCode(country.getCountryTaxCode());
                        countryRepository.save(c);
                    },
                    () -> countryRepository.save(country)
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updatePaymentMethod(PaymentMethod paymentMethod, Acknowledgment acknowledgment) {
        try {
            paymentMethodRepository.findByCode(paymentMethod.getCode()).ifPresentOrElse(
                    p -> {
                        p.setLabel(paymentMethod.getLabel());
                        p.setCode(paymentMethod.getCode());
                        p.setRequiresBankAccount(paymentMethod.getRequiresBankAccount());
                        paymentMethodRepository.save(p);
                    },
                    () -> paymentMethodRepository.save(paymentMethod)
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateActivity(Activity activity, Acknowledgment acknowledgment) {
        try {

            activityRepository.findByCode(activity.getCode()).ifPresentOrElse(
                    a -> {
                        a.setLabel(activity.getLabel());
                        a.setActive(activity.getActive());
                        activityRepository.save(a);
                    },
                    () -> activityRepository.save(activity)
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateRole(Role role, Acknowledgment acknowledgment) {
        try {
            staticRoleRepository.findByCode(role.getStaticRoleCode().getCode()).ifPresentOrElse(
                    str -> roleRepository.findByCode(role.getCode()).ifPresentOrElse(
                            r -> {
                                r.setLabel(role.getLabel());
                                r.setStaticRoleCode(str);
                                r.setActive(role.getActive());
                                roleRepository.save(r);
                            },
                            () -> roleRepository.save(role)
                    ),
                    () -> {
                        throw new ResourcesNotFoundException("AssociatedTo not found", "Role", role.getStaticRoleCode().getCode());
                    }
            );

            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteMilestone(Milestone milestone, Acknowledgment acknowledgment) {
        try {
            milestoneRepository.deleteByCode(milestone.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deletePhase(Phase phase, Acknowledgment acknowledgment) {
        try {
            phaseRepository.deleteByCode(phase.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteLegalCategory(LegalCategory legalCategory, Acknowledgment acknowledgment) {
        try {
            legalCategoryRepository.deleteByCode(legalCategory.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteDelegation(Delegation delegation, Acknowledgment acknowledgment) {
        try {
            delegationRepository.deleteByCode(delegation.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteCurrency(Currency currency, Acknowledgment acknowledgment) {
        try {
            currencyRepository.deleteByCode(currency.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteCountry(Country country, Acknowledgment acknowledgment) {
        try {
            countryRepository.deleteByCode(country.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deletePaymentMethod(PaymentMethod paymentMethod, Acknowledgment acknowledgment) {
        try {
            paymentMethodRepository.deleteByCode(paymentMethod.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteActivity(Activity activity, Acknowledgment acknowledgment) {
        try {
            activityRepository.deleteByCode(activity.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteRole(Role role, Acknowledgment acknowledgment) {
        try {
            roleRepository.deleteByCode(role.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewProduct(Product product, Acknowledgment acknowledgment) {
        try {
            if (product.getActivity() == null) {
                throw new ResourcesNotFoundException("Activity not found", "Activity", Product.class.getSimpleName());
            }
            var activity = activityRepository.findByCode(product.getActivity().getCode())
                    .orElseThrow(() ->
                            new ResourcesNotFoundException("Activity not found", "Activity", Product.class.getSimpleName()));
            product.setActivity(activity);
            productRepository.save(product);

            final String activityCode = product.getActivity().getCode();

            System.out.println("Saving product with activity code: " + activityCode);

            // Register synchronization with explicit TransactionSynchronizationManager
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    try {
                        // Correct URL - check if it needs /api/ prefix or not based on your controller mapping
                        restTemplate.exchange(
                                ACTOR_SERVER_URL + "actors/actor-activity-products/" + activityCode + "/add-new-product",
                                HttpMethod.POST,
                                null,
                                Void.class
                        );
                    } catch (Exception restException) {
                        throw new TechnicalException(
                                "Error while calling actor service to add new product: " + restException.getMessage(),
                                restException.getMessage(),
                                MODEL_NAME
                        );
                    }
                }
            });

            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateProduct(Product product, Acknowledgment acknowledgment) {
        try {
            if (product.getActivity() == null) {
                throw new ResourcesNotFoundException("Activity not found", "Activity", Product.class.getSimpleName());
            }
            var activity = activityRepository.findByCode(product.getActivity().getCode())
                    .orElseThrow(() ->
                            new ResourcesNotFoundException("Activity not found", "Activity", Product.class.getSimpleName()));
            // Store needed values for the API call
            final String activityCode = activity.getCode();
            final String productCode = product.getCode();
            final boolean isActive = product.getActive();
            productRepository.findByCode(product.getCode()).ifPresentOrElse(
                    p -> {
                        p.setLabel(product.getLabel());
                        p.setCode(product.getCode());
                        p.setActivity(activity);
                        p.setActive(product.getActive());
                        productRepository.save(p);
                    },
                    () -> productRepository.save(product)
            );
            // Register synchronization with TransactionSynchronizationManager
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    try {
                        PatchActorActivityProductDTO patchDto = new PatchActorActivityProductDTO(
                                activityCode,
                                productCode,
                                isActive
                        );

                        restTemplate.exchange(
                                ACTOR_SERVER_URL + "actors/actor-activity-products",
                                HttpMethod.PUT,
                                new org.springframework.http.HttpEntity<>(patchDto),
                                Void.class
                        );

                    } catch (Exception restException) {
                        throw new TechnicalException(
                                "Error while calling actor service to update product: " + restException.getMessage(),
                                restException.getMessage(),
                                MODEL_NAME
                        );
                    }
                }
            });
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteProduct(Product product, Acknowledgment acknowledgment) {
        try {
            actorActivityProductRepository.deleteAllByActivityCodeAndProductCode(
                    product.getActivity().getCode(),
                    product.getCode()
            );
            productLineTypeMappingRepository.deleteByProduct_Code(product.getCode());
            productRepository.deleteByCode(product.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewLineType(LineType lineType, Acknowledgment acknowledgment) {
        lineTypeRepository.save(lineType);
        acknowledgment.acknowledge();
    }

    @Transactional
    public void updateLineType(LineType lineType, Acknowledgment acknowledgment) {
        lineTypeRepository.findByCode(lineType.getCode()).ifPresentOrElse(
                l -> {
                    l.setLabel(lineType.getLabel());
                    lineTypeRepository.save(l);
                },
                () -> lineTypeRepository.save(lineType)
        );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void deleteLineType(LineType lineType, Acknowledgment acknowledgment) {
        lineTypeRepository.deleteByCode(lineType.getCode());
        acknowledgment.acknowledge();
    }

    @Transactional
    public void createNewOperation(Operation operation, Acknowledgment acknowledgment) {
        operationRepository.save(operation);
        acknowledgment.acknowledge();
    }

    @Transactional
    public void updateOperation(Operation operation, Acknowledgment acknowledgment) {
        operationRepository.findByCode(operation.getCode()).ifPresentOrElse(
                o -> {
                    o.setLabel(operation.getLabel());
                    o.setActive(operation.getActive());
                    operationRepository.save(o);
                },
                () -> operationRepository.save(operation)
        );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void deleteOperation(Operation operation, Acknowledgment acknowledgment) {
        operationRepository.deleteByCode(operation.getCode());
        acknowledgment.acknowledge();
    }

    @Transactional
    public void createNewNature(Nature nature, Acknowledgment acknowledgment) {
        natureRepository.save(nature);
        acknowledgment.acknowledge();
    }

    @Transactional
    public void updateNature(Nature nature, Acknowledgment acknowledgment) {
        natureRepository.findByCode(nature.getCode()).ifPresentOrElse(
                n -> {
                    n.setLabel(nature.getLabel());
                    natureRepository.save(n);
                },
                () -> natureRepository.save(nature)
        );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void deleteNature(Nature nature, Acknowledgment acknowledgment) {
        natureRepository.deleteByCode(nature.getCode());
        acknowledgment.acknowledge();
    }

    @Transactional
    public void createNewOperationNatureMapping(OperationNatureMapping operationNatureMapping, Acknowledgment acknowledgment) {
        operationNatureMapping.setOperation(operationRepository.findByCode(operationNatureMapping.getOperation().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Operation not found", "Operation", OperationNatureMapping.class.getSimpleName())));
        operationNatureMapping.setNature(natureRepository.findByCode(operationNatureMapping.getNature().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Nature not found", "Nature", OperationNatureMapping.class.getSimpleName())));
        operationNatureMappingRepository.save(operationNatureMapping);
        acknowledgment.acknowledge();
    }

    @Transactional
    public void updateOperationNatureMapping(OperationNatureMapping operationNatureMapping, Acknowledgment acknowledgment) {
        operationNatureMapping.setOperation(operationRepository.findByCode(operationNatureMapping.getOperation().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Operation not found", "Operation", OperationNatureMapping.class.getSimpleName())));
        operationNatureMapping.setNature(natureRepository.findByCode(operationNatureMapping.getNature().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Nature not found", "Nature", OperationNatureMapping.class.getSimpleName())));
        operationNatureMappingRepository.findByOperationIdAndNatureId(operationNatureMapping.getOperation().getId()
                , operationNatureMapping.getNature().getId()).ifPresentOrElse(
                o -> {
                    o.setNatureStatus(operationNatureMapping.getNatureStatus());
                    operationNatureMappingRepository.save(o);
                },
                () -> operationNatureMappingRepository.save(operationNatureMapping)
        );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void deleteOperationNatureMapping(OperationNatureMapping operationNatureMapping, Acknowledgment acknowledgment) {
        operationNatureMappingRepository.deleteByOperationNatureCode(operationNatureMapping.getOperationNatureCode());
        acknowledgment.acknowledge();
    }

    @Transactional
    public void createNewOperationNatureTypeMapping(OperationNatureTypeMapping operationNatureTypeMapping, Acknowledgment acknowledgment) {
        operationNatureMappingRepository.findByOperationNatureCode(operationNatureTypeMapping.getOperationNatureMapping().getOperationNatureCode())
                .ifPresentOrElse(
                        operationNatureTypeMapping::setOperationNatureMapping,
                        () -> {
                            throw new ResourcesNotFoundException("OperationNatureMapping not found", "OperationNatureMapping", OperationNatureTypeMapping.class.getSimpleName());
                        }
                );
        lineTypeRepository.findByCode(operationNatureTypeMapping.getType().getCode())
                .ifPresentOrElse(
                        operationNatureTypeMapping::setType,
                        () -> {
                            throw new ResourcesNotFoundException("LineType not found", "LineType", OperationNatureTypeMapping.class.getSimpleName());
                        }
                );
        operationNatureTypeMappingRepository.save(operationNatureTypeMapping);
        acknowledgment.acknowledge();
    }

    @Transactional
    public void updateOperationNatureTypeMapping(OperationNatureTypeMapping operationNatureTypeMapping, Acknowledgment acknowledgment) {
        operationNatureMappingRepository.findByOperationNatureCode(operationNatureTypeMapping.getOperationNatureMapping().getOperationNatureCode())
                .ifPresentOrElse(
                        operationNatureTypeMapping::setOperationNatureMapping,
                        () -> {
                            throw new ResourcesNotFoundException("OperationNatureMapping not found", "OperationNatureMapping", OperationNatureTypeMapping.class.getSimpleName());
                        }
                );
        lineTypeRepository.findByCode(operationNatureTypeMapping.getType().getCode())
                .ifPresentOrElse(
                        operationNatureTypeMapping::setType,
                        () -> {
                            throw new ResourcesNotFoundException("LineType not found", "LineType", OperationNatureTypeMapping.class.getSimpleName());
                        }
                );
        operationNatureTypeMappingRepository.findByOperationNatureTypeMappingCode(operationNatureTypeMapping.getOperationNatureTypeMappingCode())
                .ifPresentOrElse(
                        o -> {
                            o.setTypeStatus(operationNatureTypeMapping.getTypeStatus());
                            operationNatureTypeMappingRepository.save(o);
                        },
                        () -> operationNatureTypeMappingRepository.save(operationNatureTypeMapping)
                );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void deleteOperationNatureTypeMapping(OperationNatureTypeMapping operationNatureTypeMapping, Acknowledgment acknowledgment) {
        operationNatureTypeMappingRepository.deleteByOperationNatureTypeMappingCode(operationNatureTypeMapping.getOperationNatureTypeMappingCode());
        acknowledgment.acknowledge();
    }

    @Transactional
    public void createNewProductLineTypeMapping(ProductLineTypeMapping productLineTypeMapping, Acknowledgment acknowledgment) {
        productLineTypeMapping.setProduct(productRepository.findByCode(productLineTypeMapping.getProduct().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Product not found", "Product", ProductLineTypeMapping.class.getSimpleName())));
        productLineTypeMapping.setOperationNatureTypeMapping(operationNatureTypeMappingRepository
                .findByOperationNatureTypeMappingCode(productLineTypeMapping.getOperationNatureTypeMapping().getOperationNatureTypeMappingCode())
                .orElseThrow(() -> new ResourcesNotFoundException("operation nature type not found "
                        + productLineTypeMapping.getOperationNatureTypeMapping().getOperationNatureTypeMappingCode(), "Product", ProductLineTypeMapping.class.getSimpleName())));
        productLineTypeMappingRepository.save(productLineTypeMapping);
        acknowledgment.acknowledge();
    }

    @Transactional
    public void updateProductLineTypeMapping(ProductLineTypeMapping productLineTypeMapping, Acknowledgment acknowledgment) {
        productLineTypeMapping.setProduct(productRepository.findByCode(productLineTypeMapping.getProduct().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Product not found", "Product", ProductLineTypeMapping.class.getSimpleName())));
        productLineTypeMapping.setOperationNatureTypeMapping(operationNatureTypeMappingRepository
                .findByOperationNatureTypeMappingCode(productLineTypeMapping.getOperationNatureTypeMapping().getOperationNatureTypeMappingCode())
                .orElseThrow(() -> new ResourcesNotFoundException("operation nature type not found "
                        + productLineTypeMapping.getOperationNatureTypeMapping().getOperationNatureTypeMappingCode(), "Product", ProductLineTypeMapping.class.getSimpleName())));
        productLineTypeMappingRepository.findByProduct_CodeAndOperationNatureTypeMappingId(productLineTypeMapping.getProduct().getCode()
                , productLineTypeMapping.getOperationNatureTypeMapping().getId()).ifPresentOrElse(
                p -> {
                    //p.setLineTypeStatus(productLineTypeMapping.getLineTypeStatus());
                    //productLineTypeMappingRepository.save(p);
                },
                () -> productLineTypeMappingRepository.save(productLineTypeMapping)
        );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void deleteProductLineTypeMapping(ProductLineTypeMapping productLineTypeMapping, Acknowledgment acknowledgment) {
        productLineTypeMappingRepository
                .deleteByProduct_CodeAndOperationNatureTypeMappingOperationNatureTypeMappingCode(productLineTypeMapping.getProduct().getCode(), productLineTypeMapping
                        .getOperationNatureTypeMapping().getOperationNatureTypeMappingCode());
        acknowledgment.acknowledge();
    }

    @Transactional
    public void createNewTax(Tax tax, Acknowledgment acknowledgment) {
        try {
            taxRepository.save(tax);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewTaxRate(TaxRate taxRate, Acknowledgment acknowledgment) {
        try {
            taxRepository.findByCode(taxRate.getTax().getCode()).ifPresentOrElse(
                    t -> {
                        taxRate.setTax(t);
                        taxRateRepository.save(taxRate);
                    },
                    () -> {
                        throw new TechnicalException(ERRORMESSAGE + "Tax not found", "Tax not found", MODEL_NAME);
                    }
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateTax(Tax tax, Acknowledgment acknowledgment) {
        try {
            taxRepository.findByCode((tax.getCode())).ifPresentOrElse(
                    t -> {
                        t.setStartDate(tax.getStartDate());
                        t.setEndDate(tax.getEndDate());
                        t.setCountry(tax.getCountry());
                        t.setActive(tax.getActive());
                        t.setStartDate(tax.getStartDate());
                        t.setEndDate(tax.getEndDate());
                        taxRepository.save(t);
                    },
                    () -> taxRepository.save(tax)
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateTaxRate(TaxRate taxRate, Acknowledgment acknowledgment) {
        try {
            taxRepository.findByCode(taxRate.getTax().getCode()).ifPresentOrElse(
                    t -> {
                        taxRate.setTax(t);
                        taxRateRepository.findByCode(taxRate.getCode()).ifPresentOrElse(
                                tr -> {
                                    tr.setStartDate(taxRate.getStartDate());
                                    tr.setEndDate(taxRate.getEndDate());
                                    tr.setRate(taxRate.getRate());
                                    tr.setActive(taxRate.getActive());
                                    tr.setLabel(taxRate.getLabel());
                                    taxRateRepository.save(tr);
                                },
                                () -> {
                                    taxRate.setTax(t);
                                    taxRateRepository.save(taxRate);
                                }
                        );
                    },
                    () -> {
                        throw new TechnicalException(ERRORMESSAGE + "Tax not found", "Tax not found", MODEL_NAME);
                    }
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteTax(Tax tax, Acknowledgment acknowledgment) {
        try {
            taxRepository.deleteByCode(tax.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteTaxRate(TaxRate taxeRate, Acknowledgment acknowledgment) {
        try {
            if (taxeRate.getCode() != null && taxeRate.getTax() != null && taxeRate.getTax().getCode() != null) {
                taxRateRepository.deleteByCodeAndTaxCode(taxeRate.getCode(), taxeRate.getTax().getCode());
            } else if (taxeRate.getTax() != null && taxeRate.getTax().getCode() != null) {
                taxRateRepository.deleteAllByTaxCode(taxeRate.getTax().getCode());
            }
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createLegalActivity(LegalActivities legalActivity, Acknowledgment acknowledgment) {
        try {
            legalActivitiesRepository.save(legalActivity);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateLegalActivity(LegalActivities legalActivity, Acknowledgment acknowledgment) {
        try {
            legalActivitiesRepository.findByCode(legalActivity.getCode()).ifPresentOrElse(
                    n -> {
                        n.setLabel(legalActivity.getLabel());
                        legalActivitiesRepository.save(n);
                    },
                    () -> legalActivitiesRepository.save(legalActivity)
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteLegalActivity(LegalActivities legalActivity, Acknowledgment acknowledgment) {
        try {
            legalActivitiesRepository.deleteByCode(legalActivity.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createCategory(Category category, Acknowledgment acknowledgment) {
        try {
            categoryRepository.save(category);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateCategory(Category category, Acknowledgment acknowledgment) {
        try {
            categoryRepository.findByCode(category.getCode()).ifPresentOrElse(
                    n -> {
                        n.setLabel(category.getLabel());
                        categoryRepository.save(n);
                    },
                    () -> categoryRepository.save(category)
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteCategory(Category category, Acknowledgment acknowledgment) {
        try {
            categoryRepository.deleteByCode(category.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createMarket(Market market, Acknowledgment acknowledgment) {
        try {
            marketRepository.save(market);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateMarket(Market market, Acknowledgment acknowledgment) {
        try {
            marketRepository.findByCode(market.getCode()).ifPresentOrElse(
                    m -> {
                        m.setLabel(market.getLabel());
                        m.setDescription(market.getDescription());
                        m.setActive(market.getActive());
                        marketRepository.save(m);
                    },
                    () -> marketRepository.save(market)
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteMarket(Market market, Acknowledgment acknowledgment) {
        try {
            marketRepository.deleteByCode(market.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewEquipment(Equipment equipment, Acknowledgment acknowledgment) {
        try {
            equipment.setMarket(marketRepository.findByCode(equipment.getMarket().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Market not found", "Market", Equipment.class.getSimpleName())));
            equipment.setCategory(categoryRepository.findByCode(equipment.getCategory().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Category not found", "Category", Equipment.class.getSimpleName())));
            equipment.setCountry(countryRepository.findByCode(equipment.getCountry().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Country not found", "Country", Equipment.class.getSimpleName())));
            equipmentRepository.save(equipment);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateEquipment(Equipment equipment, Acknowledgment acknowledgment) {
        try {
            Market market = marketRepository.findByCode(equipment.getMarket().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Market not found", "Market", Equipment.class.getSimpleName()));
            Category category = categoryRepository.findByCode(equipment.getCategory().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Category not found", "Category", Equipment.class.getSimpleName()));
            Country country = countryRepository.findByCode(equipment.getCountry().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Country not found", "Country", Equipment.class.getSimpleName()));

            equipmentRepository.findByCode(equipment.getCode()).ifPresentOrElse(
                    e -> {
                        e.setLabel(equipment.getLabel());
                        e.setDescription(equipment.getDescription());
                        e.setSystemAttribute(equipment.getSystemAttribute());
                        e.setActive(equipment.getActive());
                        e.setMarket(market);
                        e.setCategory(category);
                        e.setCountry(country);
                        equipmentRepository.save(e);
                    },
                    () -> {
                        equipment.setMarket(market);
                        equipment.setCategory(category);
                        equipment.setCountry(country);
                        equipmentRepository.save(equipment);
                    }
            );

            equipmentRepository.save(equipment);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteEquipment(Equipment equipment, Acknowledgment acknowledgment) {
        try {
            equipmentRepository.deleteByCode(equipment.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewScale(ScaleStreamDto scaleStreamDto, Acknowledgment acknowledgment) {
        try {
            var nature = staticParameterRepository.findByCode(scaleStreamDto.getNature())
                    .orElseThrow(() -> new ResourcesNotFoundException("Nature not found", "Nature", Scale.class.getSimpleName()));
            if ("MT".equals(nature.getCode())) {
                ScaleElementMaterial scaleElementMaterial = new ScaleElementMaterial();
                buildScaleElementMaterial(scaleElementMaterial, scaleStreamDto);
                scaleElementMaterialsRepository.save(scaleElementMaterial);
            } else if ("VH".equals(nature.getCode())) {
                ScaleElementVehicle scaleElementVehicle = new ScaleElementVehicle();
                buildScaleElementVehicle(scaleElementVehicle, scaleStreamDto);
                scaleElementVehicleRepository.save(scaleElementVehicle);
            }
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateScale(ScaleStreamDto scaleStreamDto, Acknowledgment acknowledgment) {
        try {
            var nature = staticParameterRepository.findByCode(scaleStreamDto.getNature())
                    .orElseThrow(() -> new ResourcesNotFoundException("Nature not found", "Nature", Scale.class.getSimpleName()));
            if ("MT".equals(nature.getCode())) {
                ScaleElementMaterial scaleElementMaterial = scaleElementMaterialsRepository
                        .findByReferenceAndDeletedAtIsNull(scaleStreamDto.getReference())
                        .orElseThrow(() -> new ResourcesNotFoundException("Scale not found", "Scale", Scale.class.getSimpleName()));
                buildScaleElementMaterial(scaleElementMaterial, scaleStreamDto);
                scaleElementMaterialsRepository.save(scaleElementMaterial);
            } else if ("VH".equals(nature.getCode())) {
                ScaleElementVehicle scaleElementVehicle = scaleElementVehicleRepository
                        .findByReferenceAndDeletedAtIsNull(scaleStreamDto.getReference())
                        .orElseThrow(() -> new ResourcesNotFoundException("Scale not found", "Scale", Scale.class.getSimpleName()));
                buildScaleElementVehicle(scaleElementVehicle, scaleStreamDto);
                scaleElementVehicleRepository.save(scaleElementVehicle);
            }
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteScale(ScaleStreamDto scale, Acknowledgment acknowledgment) {
        scaleRepository.findByCode(scale.getCode()).ifPresent(
                e -> {
                    e.setDeletedAt(new Date());
                    scaleRepository.save(e);
                }
        );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void createNewApplicationCriteria(ApplicationCriteria applicationCriteria, Acknowledgment acknowledgment) {
        var scale = scaleRepository.findByCode(applicationCriteria.getScale().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Scale not found when creating new application criteria", "Scale", Scale.class.getSimpleName()));
        var currency = currencyRepository.findByCode(applicationCriteria.getCurrency().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Currency not found", "Currency", Scale.class.getSimpleName()));
        applicationCriteria.setCurrency(currency);
        applicationCriteria.setScale(scale);
        applicationCriteriaRepository.save(applicationCriteria);
        acknowledgment.acknowledge();
    }

    @Transactional
    public void updateApplicationCriteria(ApplicationCriteria applicationCriteria, Acknowledgment acknowledgment) {
        var oldApplicationCriteria = applicationCriteriaRepository.findByReference(applicationCriteria.getReference())
                .orElseThrow(() -> new ResourcesNotFoundException("applicationCriteria not found", "Scale", Scale.class.getSimpleName()));
        oldApplicationCriteria.setCurrency(currencyRepository.findByCode(applicationCriteria.getCurrency().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Currency not found", "Currency", Scale.class.getSimpleName())));
        oldApplicationCriteria.setChannelOfAcquisition(applicationCriteria.getChannelOfAcquisition());
        oldApplicationCriteria.setCustomerType(applicationCriteria.getCustomerType());
        oldApplicationCriteria.setFinancialScoring(applicationCriteria.getFinancialScoring());

        applicationCriteriaRepository.save(oldApplicationCriteria);
        acknowledgment.acknowledge();
    }

    @Transactional
    public void deleteApplicationCriteria(ApplicationCriteria applicationCriteria, Acknowledgment acknowledgment) {
        applicationCriteriaRepository.findByReference(applicationCriteria.getReference()).ifPresent(
                e -> applicationCriteriaRepository.deleteByReference(applicationCriteria.getReference())
        );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void createNewApplicationCriteriaActor(ApplicationCriteriaActors applicationCriteriaActors, Acknowledgment acknowledgment) {
        var applicationCriteria = applicationCriteriaRepository.findByReference(applicationCriteriaActors.getApplicationCriteria().getReference())
                .orElseThrow(() -> new ResourcesNotFoundException("applicationCriteria not found", "Scale", Scale.class.getSimpleName()));
        var actor = actorRepository.findByReferenceAndDeletedAtIsNull(applicationCriteriaActors.getActor().getReference())
                .orElseThrow(() -> new ResourcesNotFoundException("Actor not found", "Actor", Scale.class.getSimpleName()));
        applicationCriteriaActors.setApplicationCriteria(applicationCriteria);
        applicationCriteriaActors.setActor(actor);
        applicationCriteriaActorsRepository.save(applicationCriteriaActors);
        acknowledgment.acknowledge();
    }

    @Transactional
    public void updateApplicationCriteriaActor(ApplicationCriteriaActors applicationCriteriaActors, Acknowledgment acknowledgment) {
        applicationCriteriaActorsRepository.findByReference(applicationCriteriaActors.getReference()).ifPresentOrElse(
                a -> {
                    a.setActor(actorRepository.findByReferenceAndDeletedAtIsNull(applicationCriteriaActors.getActor().getReference())
                            .orElseThrow(() -> new ResourcesNotFoundException("Actor not found", "Actor", Scale.class.getSimpleName())));
                    applicationCriteriaActorsRepository.save(a);
                },
                () -> applicationCriteriaActorsRepository.save(applicationCriteriaActors)
        );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void deleteApplicationCriteriaActor(ApplicationCriteriaActors applicationCriteriaActors, Acknowledgment acknowledgment) {
        applicationCriteriaActorsRepository.findByReference(applicationCriteriaActors.getReference()).ifPresent(
                e -> applicationCriteriaActorsRepository.deleteByReference(applicationCriteriaActors.getReference())
        );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void createNewScaleFinancialProduct(ScaleFinancialProduct scaleFinancialProduct, Acknowledgment acknowledgment) {
        var scale = scaleRepository.findByCode(scaleFinancialProduct.getScale().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Scale not found", "Scale", Scale.class.getSimpleName()));
        var financialProduct = productRepository.findByCode(scaleFinancialProduct.getProduct().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Financial product not found", "Financial product", Scale.class.getSimpleName()));
        scaleFinancialProduct.setScale(scale);
        scaleFinancialProduct.setProduct(financialProduct);
        scaleFinancialProductRepository.save(scaleFinancialProduct);
        acknowledgment.acknowledge();
    }

    @Transactional
    public void updateScaleFinancialProduct(ScaleFinancialProduct scaleFinancialProduct, Acknowledgment acknowledgment) {
        scaleFinancialProductRepository.findByReference(scaleFinancialProduct.getReference()).ifPresentOrElse(
                s -> {
                    s.setScale(scaleRepository.findByCode(scaleFinancialProduct.getScale().getCode())
                            .orElseThrow(() -> new ResourcesNotFoundException("Scale not found", "Scale", Scale.class.getSimpleName())));
                    s.setProduct(productRepository.findByCode(scaleFinancialProduct.getProduct().getCode())
                            .orElseThrow(() -> new ResourcesNotFoundException("Financial product not found", "Financial product", Scale.class.getSimpleName())));
                    scaleFinancialProductRepository.save(s);
                },
                () -> scaleFinancialProductRepository.save(scaleFinancialProduct)
        );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void deleteScaleFinancialProduct(ScaleFinancialProduct scaleFinancialProduct, Acknowledgment acknowledgment) {
        scaleFinancialProductRepository.findByReference(scaleFinancialProduct.getReference()).ifPresent(
                e -> scaleFinancialProductRepository.deleteByReference(scaleFinancialProduct.getReference())
        );
        acknowledgment.acknowledge();
    }

    private void buildScaleElementMaterial(ScaleElementMaterial scaleElementMaterial, ScaleStreamDto scaleStreamDto) {
        // Required repository lookups
        if (scaleStreamDto.getCurrency_code() != null) {
            scaleElementMaterial.setCurrency(currencyRepository.findByCode(scaleStreamDto.getCurrency_code())
                    .orElseThrow(() -> new ResourcesNotFoundException("Currency not found", "Currency", Scale.class.getSimpleName())));
        }

        if (scaleStreamDto.getNature() != null) {
            scaleElementMaterial.setNature(staticParameterRepository.findByCode(scaleStreamDto.getNature())
                    .orElseThrow(() -> new ResourcesNotFoundException("Nature not found", "Nature", Scale.class.getSimpleName())));
        }

        // Simple fields
        if (scaleStreamDto.getCode() != null) scaleElementMaterial.setCode(scaleStreamDto.getCode());
        if (scaleStreamDto.getReference() != null) scaleElementMaterial.setReference(scaleStreamDto.getReference());
        if (scaleStreamDto.getLabel() != null) scaleElementMaterial.setLabel(scaleStreamDto.getLabel());
        if (scaleStreamDto.getDescription() != null)
            scaleElementMaterial.setDescription(scaleStreamDto.getDescription());
        if (scaleStreamDto.getStart_date() != null) scaleElementMaterial.setStartDate(scaleStreamDto.getStart_date());
        if (scaleStreamDto.getEnd_date() != null) scaleElementMaterial.setEndDate(scaleStreamDto.getEnd_date());
        if (scaleStreamDto.getMaximum_eligible_amount() != null)
            scaleElementMaterial.setMaximumEligibleAmount(scaleStreamDto.getMaximum_eligible_amount());
        if (scaleStreamDto.getMinimum_eligible_amount() != null)
            scaleElementMaterial.setMinimumEligibleAmount(scaleStreamDto.getMinimum_eligible_amount());
        if (scaleStreamDto.getRate_period() != null)
            scaleElementMaterial.setRatePeriod(scaleStreamDto.getRate_period());
        if (scaleStreamDto.getMaximum_financing_duration() != null)
            scaleElementMaterial.setMaximumFinancingPeriod(scaleStreamDto.getMaximum_financing_duration());
        if (scaleStreamDto.getMinimum_financing_duration() != null)
            scaleElementMaterial.setMinimumFinancingPeriod(scaleStreamDto.getMinimum_financing_duration());
        if (scaleStreamDto.getMaximum_residual_value() != null)
            scaleElementMaterial.setMaximumResidualValue(scaleStreamDto.getMaximum_residual_value());
        if (scaleStreamDto.getMinimum_residual_value() != null)
            scaleElementMaterial.setMinimumResidualValue(scaleStreamDto.getMinimum_residual_value());
        if (scaleStreamDto.getMaximum_personal_contribution() != null)
            scaleElementMaterial.setMaximumPersonalContribution(scaleStreamDto.getMaximum_personal_contribution());
        if (scaleStreamDto.getMinimum_personal_contribution() != null)
            scaleElementMaterial.setMinimumPersonalContribution(scaleStreamDto.getMinimum_personal_contribution());
        if (scaleStreamDto.getMaximum_security_deposit() != null)
            scaleElementMaterial.setMaximumSecurityDeposit(scaleStreamDto.getMaximum_security_deposit());
        if (scaleStreamDto.getMinimum_security_deposit() != null)
            scaleElementMaterial.setMinimumSecurityDeposit(scaleStreamDto.getMinimum_security_deposit());
        if (scaleStreamDto.getAsset_usage() != null)
            scaleElementMaterial.setAssetUsage(scaleStreamDto.getAsset_usage());
        if (scaleStreamDto.getCondition() != null) scaleElementMaterial.setCondition(scaleStreamDto.getCondition());
        if (scaleStreamDto.getMaximum_interest_rate() != null)
            scaleElementMaterial.setMaximumInterestRate(scaleStreamDto.getMaximum_interest_rate());
        if (scaleStreamDto.getMinimum_interest_rate() != null)
            scaleElementMaterial.setMinimumInterestRate(scaleStreamDto.getMinimum_interest_rate());
        if (scaleStreamDto.getNominal_interest_rate() != null)
            scaleElementMaterial.setNominalInterestRate(scaleStreamDto.getNominal_interest_rate());
        if (scaleStreamDto.getHas_personal_contribution() != null)
            scaleElementMaterial.setHasPersonalContribution(scaleStreamDto.getHas_personal_contribution());
        if (scaleStreamDto.getGrace_period_duration() != null)
            scaleElementMaterial.setGracePeriodDuration(scaleStreamDto.getGrace_period_duration());
        if (scaleStreamDto.getHas_security_deposit() != null)
            scaleElementMaterial.setHasSecurityDeposit(scaleStreamDto.getHas_security_deposit());
        if (scaleStreamDto.getWith_interest_payment() != null)
            scaleElementMaterial.setWithInterestPayment(scaleStreamDto.getWith_interest_payment());
        if (scaleStreamDto.getHas_grace_period() != null)
            scaleElementMaterial.setHasGracePeriod(scaleStreamDto.getHas_grace_period());
        if (scaleStreamDto.getStatus() != null) scaleElementMaterial.setStatus(scaleStreamDto.getStatus());
        if (scaleStreamDto.getDesignation() != null)
            scaleElementMaterial.setDesignation(scaleStreamDto.getDesignation());

        if (scaleStreamDto.getMarket_code() != null) {
            scaleElementMaterial.setMarket(marketRepository.findByCode(scaleStreamDto.getMarket_code())
                    .orElseThrow(() -> new ResourcesNotFoundException("Market not found", "Market", Scale.class.getSimpleName())));
        }

        if (scaleStreamDto.getApplication_criteria_reference() != null) {
            applicationCriteriaRepository.findByReference(scaleStreamDto.getApplication_criteria_reference())
                    .ifPresentOrElse(
                            scaleElementMaterial::setApplicationCriteria,
                            () -> {
                                throw new ResourcesNotFoundException("ApplicationCriteria not found",
                                        "ApplicationCriteria",
                                        scaleStreamDto.getApplication_criteria_reference().toString());
                            }
                    );
        }
    }

    private void buildScaleElementVehicle(ScaleElementVehicle scaleElementVehicle, ScaleStreamDto scaleStreamDto) {
        // Required repository lookups
        if (scaleStreamDto.getCurrency_code() != null) {
            scaleElementVehicle.setCurrency(currencyRepository.findByCode(scaleStreamDto.getCurrency_code())
                    .orElseThrow(() -> new ResourcesNotFoundException("Currency not found", "Currency", Scale.class.getSimpleName())));
        }

        if (scaleStreamDto.getNature() != null) {
            scaleElementVehicle.setNature(staticParameterRepository.findByCode(scaleStreamDto.getNature())
                    .orElseThrow(() -> new ResourcesNotFoundException("Nature not found", "Nature", Scale.class.getSimpleName())));
        }

        // Simple fields
        if (scaleStreamDto.getCode() != null) scaleElementVehicle.setCode(scaleStreamDto.getCode());
        if (scaleStreamDto.getReference() != null) scaleElementVehicle.setReference(scaleStreamDto.getReference());
        if (scaleStreamDto.getLabel() != null) scaleElementVehicle.setLabel(scaleStreamDto.getLabel());
        if (scaleStreamDto.getDescription() != null)
            scaleElementVehicle.setDescription(scaleStreamDto.getDescription());
        if (scaleStreamDto.getStart_date() != null) scaleElementVehicle.setStartDate(scaleStreamDto.getStart_date());
        if (scaleStreamDto.getEnd_date() != null) scaleElementVehicle.setEndDate(scaleStreamDto.getEnd_date());
        if (scaleStreamDto.getMaximum_eligible_amount() != null)
            scaleElementVehicle.setMaximumEligibleAmount(scaleStreamDto.getMaximum_eligible_amount());
        if (scaleStreamDto.getMinimum_eligible_amount() != null)
            scaleElementVehicle.setMinimumEligibleAmount(scaleStreamDto.getMinimum_eligible_amount());
        if (scaleStreamDto.getRate_period() != null) scaleElementVehicle.setRatePeriod(scaleStreamDto.getRate_period());
        if (scaleStreamDto.getMaximum_financing_duration() != null)
            scaleElementVehicle.setMaximumFinancingPeriod(scaleStreamDto.getMaximum_financing_duration());
        if (scaleStreamDto.getMinimum_financing_duration() != null)
            scaleElementVehicle.setMinimumFinancingPeriod(scaleStreamDto.getMinimum_financing_duration());
        if (scaleStreamDto.getMaximum_residual_value() != null)
            scaleElementVehicle.setMaximumResidualValue(scaleStreamDto.getMaximum_residual_value());
        if (scaleStreamDto.getMinimum_residual_value() != null)
            scaleElementVehicle.setMinimumResidualValue(scaleStreamDto.getMinimum_residual_value());
        if (scaleStreamDto.getMaximum_personal_contribution() != null)
            scaleElementVehicle.setMaximumPersonalContribution(scaleStreamDto.getMaximum_personal_contribution());
        if (scaleStreamDto.getMinimum_personal_contribution() != null)
            scaleElementVehicle.setMinimumPersonalContribution(scaleStreamDto.getMinimum_personal_contribution());
        if (scaleStreamDto.getMaximum_security_deposit() != null)
            scaleElementVehicle.setMaximumSecurityDeposit(scaleStreamDto.getMaximum_security_deposit());
        if (scaleStreamDto.getMinimum_security_deposit() != null)
            scaleElementVehicle.setMinimumSecurityDeposit(scaleStreamDto.getMinimum_security_deposit());
        if (scaleStreamDto.getAsset_usage() != null) scaleElementVehicle.setAssetUsage(scaleStreamDto.getAsset_usage());
        if (scaleStreamDto.getCondition() != null) scaleElementVehicle.setCondition(scaleStreamDto.getCondition());
        if (scaleStreamDto.getMaximum_interest_rate() != null)
            scaleElementVehicle.setMaximumInterestRate(scaleStreamDto.getMaximum_interest_rate());
        if (scaleStreamDto.getMinimum_interest_rate() != null)
            scaleElementVehicle.setMinimumInterestRate(scaleStreamDto.getMinimum_interest_rate());
        if (scaleStreamDto.getNominal_interest_rate() != null)
            scaleElementVehicle.setNominalInterestRate(scaleStreamDto.getNominal_interest_rate());
        if (scaleStreamDto.getHas_personal_contribution() != null)
            scaleElementVehicle.setHasPersonalContribution(scaleStreamDto.getHas_personal_contribution());
        if (scaleStreamDto.getGrace_period_duration() != null)
            scaleElementVehicle.setGracePeriodDuration(scaleStreamDto.getGrace_period_duration());
        if (scaleStreamDto.getHas_security_deposit() != null)
            scaleElementVehicle.setHasSecurityDeposit(scaleStreamDto.getHas_security_deposit());
        if (scaleStreamDto.getWith_interest_payment() != null)
            scaleElementVehicle.setWithInterestPayment(scaleStreamDto.getWith_interest_payment());
        if (scaleStreamDto.getHas_grace_period() != null)
            scaleElementVehicle.setHasGracePeriod(scaleStreamDto.getHas_grace_period());
        if (scaleStreamDto.getMaximum_mileage() != null)
            scaleElementVehicle.setMaximumMileage(scaleStreamDto.getMaximum_mileage());
        if (scaleStreamDto.getMinimum_mileage() != null)
            scaleElementVehicle.setMinimumMileage(scaleStreamDto.getMinimum_mileage());
        if (scaleStreamDto.getStatus() != null) scaleElementVehicle.setStatus(scaleStreamDto.getStatus());

        // Market lookup is commented out in the original code, maintaining the same pattern
        // if (scaleStreamDto.getMarket_code() != null) {
        //    scaleElementVehicle.setMarket(marketRepository.findByCode(scaleStreamDto.getMarket_code())
        //            .orElseThrow(() -> new ResourcesNotFoundException("Market not found", "Market", Scale.class.getSimpleName())));
        // }

        if (scaleStreamDto.getApplication_criteria_reference() != null) {
            applicationCriteriaRepository.findByReference(scaleStreamDto.getApplication_criteria_reference())
                    .ifPresentOrElse(
                            scaleElementVehicle::setApplicationCriteria,
                            () -> {
                                throw new ResourcesNotFoundException("ApplicationCriteria not found",
                                        "ApplicationCriteria",
                                        scaleStreamDto.getApplication_criteria_reference().toString());
                            }
                    );
        }
    }

    @Transactional
    public void createNewFinancialElementMaterialEquipmentMapping(ScaleElementMaterialEquipment financialElementMaterialEquipmentMapping, Acknowledgment acknowledgment) {
        var scaleElementMaterial = scaleElementMaterialsRepository
                .findByCodeAndDeletedAtIsNull(financialElementMaterialEquipmentMapping.getScaleElementMaterial().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Scale not found", "Scale", Scale.class.getSimpleName()));
        var equipment = equipmentRepository.findByCode(financialElementMaterialEquipmentMapping.getEquipment().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Equipment not found", "Equipment", Scale.class.getSimpleName()));
        financialElementMaterialEquipmentMapping.setScaleElementMaterial(scaleElementMaterial);
        financialElementMaterialEquipmentMapping.setEquipment(equipment);
        scaleElementMaterialsEquipmentRepository.save(financialElementMaterialEquipmentMapping);
        acknowledgment.acknowledge();
    }

    @Transactional
    public void updateFinancialElementMaterialEquipmentMapping(ScaleElementMaterialEquipment financialElementMaterialEquipmentMapping, Acknowledgment acknowledgment) {
        scaleElementMaterialsEquipmentRepository.findByReference(financialElementMaterialEquipmentMapping.getReference()).ifPresentOrElse(
                f -> {
                    f.setScaleElementMaterial(scaleElementMaterialsRepository
                            .findByCodeAndDeletedAtIsNull(financialElementMaterialEquipmentMapping.getScaleElementMaterial().getCode())
                            .orElseThrow(() -> new ResourcesNotFoundException("Scale not found", "Scale", Scale.class.getSimpleName())));
                    f.setEquipment(equipmentRepository.findByCode(financialElementMaterialEquipmentMapping.getEquipment().getCode())
                            .orElseThrow(() -> new ResourcesNotFoundException("Equipment not found", "Equipment", Scale.class.getSimpleName())));
                    scaleElementMaterialsEquipmentRepository.save(f);
                },
                () -> scaleElementMaterialsEquipmentRepository.save(financialElementMaterialEquipmentMapping)
        );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void deleteFinancialElementMaterialEquipmentMapping(ScaleElementMaterialEquipment financialElementMaterialEquipmentMapping, Acknowledgment acknowledgment) {
        scaleElementMaterialsEquipmentRepository.findByReference(financialElementMaterialEquipmentMapping.getReference()).ifPresent(
                e -> scaleElementMaterialsEquipmentRepository.deleteByReference(financialElementMaterialEquipmentMapping.getReference())
        );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void createNewFinancialElementVehicleVehicleMapping(ScaleElementVehicleVehicle financialElementVehicleVehicleMapping, Acknowledgment acknowledgment) {
        var scaleElementVehicle = scaleElementVehicleRepository
                .findByCodeAndDeletedAtIsNull(financialElementVehicleVehicleMapping.getScaleElementVehicle().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Scale not found", "Scale", Scale.class.getSimpleName()));
        var vehicleStock = vehicleStockRepository.findByReference(
                        (financialElementVehicleVehicleMapping.getVehicleStock().getReference()))
                .orElseThrow(() -> new ResourcesNotFoundException("Vehicle not found", "Vehicle", Scale.class.getSimpleName()));
        financialElementVehicleVehicleMapping.setScaleElementVehicle(scaleElementVehicle);
        financialElementVehicleVehicleMapping.setVehicleStock(vehicleStock);
        scaleElementVehicleVehicleRepository.save(financialElementVehicleVehicleMapping);
        acknowledgment.acknowledge();
    }

    @Transactional
    public void updateFinancialElementVehicleVehicleMapping(ScaleElementVehicleVehicle financialElementVehicleVehicleMapping, Acknowledgment acknowledgment) {
        scaleElementVehicleVehicleRepository.findByReference(financialElementVehicleVehicleMapping.getReference()).ifPresentOrElse(
                f -> {
                    f.setScaleElementVehicle(scaleElementVehicleRepository
                            .findByCodeAndDeletedAtIsNull(financialElementVehicleVehicleMapping.getScaleElementVehicle().getCode())
                            .orElseThrow(() -> new ResourcesNotFoundException("Scale not found", "Scale", Scale.class.getSimpleName())));
                    f.setVehicleStock(vehicleStockRepository.findByReference(
                                    (financialElementVehicleVehicleMapping.getVehicleStock().getReference()))
                            .orElseThrow(() -> new ResourcesNotFoundException("Vehicle not found", "Vehicle", Scale.class.getSimpleName())));
                    scaleElementVehicleVehicleRepository.save(f);
                },
                () -> scaleElementVehicleVehicleRepository.save(financialElementVehicleVehicleMapping)
        );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void deleteFinancialElementVehicleVehicleMapping(ScaleElementVehicleVehicle financialElementVehicleVehicleMapping, Acknowledgment acknowledgment) {
        scaleElementVehicleVehicleRepository.findByReference(financialElementVehicleVehicleMapping.getReference()).ifPresent(
                e -> scaleElementVehicleVehicleRepository.deleteByReference(financialElementVehicleVehicleMapping.getReference())
        );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void createNewThirdParties(ThirdParty thirdParties, Acknowledgment acknowledgment) {
        try {
            thirdParties.setActor(actorRepository.findByReference(thirdParties.getActor().getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Actor not found", "Actor", ThirdParty.class.getSimpleName())));
            thirdParties.setRole(roleRepository.findByCode(thirdParties.getRole().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Role not found", "Role", ThirdParty.class.getSimpleName())));
            thirdParties.setScale(scaleRepository.findByCode(thirdParties.getScale().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Scale not found", "Scale", ThirdParty.class.getSimpleName())));
            thirdPartyRepository.save(thirdParties);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateThirdParties(ThirdParty thirdParties, Acknowledgment acknowledgment) {
        try {
            var existingThirdParty = thirdPartyRepository.findByReference(thirdParties.getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Third party not found", "Third party", ThirdParty.class.getSimpleName()));
            existingThirdParty.setActor(actorRepository.findByReference(thirdParties.getActor().getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Actor not found", "Actor", ThirdParty.class.getSimpleName())));
            existingThirdParty.setRole(roleRepository.findByCode(thirdParties.getRole().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Role not found", "Role", ThirdParty.class.getSimpleName())));
            existingThirdParty.setScale(scaleRepository.findByCode(thirdParties.getScale().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Scale not found", "Scale", ThirdParty.class.getSimpleName())));
            thirdPartyRepository.save(thirdParties);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteThirdParties(ThirdParty thirdParties, Acknowledgment acknowledgment) {
        try {
            thirdPartyRepository.deleteByReference(thirdParties.getReference());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createScaleService(ScaleService scaleService, Acknowledgment acknowledgment) {
        try {
            scaleService.setScale(scaleRepository.findByCode(scaleService.getScale().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Scale not found", "Scale", Scale.class.getSimpleName())));
            scaleService.setService(serviceRepository.findByReference(scaleService.getService().getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Service not found", "Service", Scale.class.getSimpleName())));

            scaleServiceRepository.save(scaleService);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateScaleService(ScaleService scaleService, Acknowledgment acknowledgment) {
        try {
            var existingScaleService = scaleServiceRepository.findByReference(scaleService.getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Scale service not found", "Scale service", ScaleService.class.getSimpleName()));
            existingScaleService.setScale(scaleRepository.findByCode(scaleService.getScale().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Scale not found", "Scale", Scale.class.getSimpleName())));
            existingScaleService.setService(serviceRepository.findByReference(scaleService.getService().getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Service not found", "Service", Scale.class.getSimpleName())));
            scaleServiceRepository.save(existingScaleService);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteScaleService(ScaleService scaleService, Acknowledgment acknowledgment) {
        try {
            scaleServiceRepository.deleteByReference(scaleService.getReference());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewVehicle(VehicleStock vehicleStock, Acknowledgment acknowledgment) {
        try {
            vehicleStockRepository.save(vehicleStock);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateVehicle(VehicleStock vehicleStock, Acknowledgment acknowledgment) {
        try {
            var existingVehicleStock = vehicleStockRepository.findByReference(vehicleStock.getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Vehicle not found", "Vehicle", VehicleStock.class.getSimpleName()));
            vehicleStock.setId(existingVehicleStock.getId());
            vehicleStock.setReference(existingVehicleStock.getReference());
            vehicleStockRepository.save(existingVehicleStock);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteVehicle(VehicleStock vehicleStock, Acknowledgment acknowledgment) {
        try {
            vehicleStockRepository.deleteByReference(vehicleStock.getReference());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewPartner(Partner partner, Acknowledgment acknowledgment) {
        try {
            partner.setActor(actorRepository.findByReference(partner.getActor().getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Actor not found", "Actor", Partner.class.getSimpleName())));
            partner.setOperationNatureTypeMapping(operationNatureTypeMappingRepository
                    .findByOperationNatureTypeMappingCode(partner.getOperationNatureTypeMapping().getOperationNatureTypeMappingCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Operation nature type mapping not found", "Operation nature type mapping", Partner.class.getSimpleName())));
            partner.setCurrency(currencyRepository.findByCode(partner.getCurrency().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Currency not found", "Currency", Partner.class.getSimpleName())));
            partnerRepository.save(partner);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updatePartner(Partner partner, Acknowledgment acknowledgment) {
        try {
            var existingPartner = partnerRepository.findByReference(partner.getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Partner not found", "Partner", Partner.class.getSimpleName()));
            existingPartner.setActor(actorRepository.findByReference(partner.getActor().getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Actor not found", "Actor", Partner.class.getSimpleName())));
            existingPartner.setOperationNatureTypeMapping(operationNatureTypeMappingRepository
                    .findByOperationNatureTypeMappingCode(partner.getOperationNatureTypeMapping().getOperationNatureTypeMappingCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Operation nature type mapping not found", "Operation nature type mapping", Partner.class.getSimpleName())));
            existingPartner.setCurrency(currencyRepository.findByCode(partner.getCurrency().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Currency not found", "Currency", Partner.class.getSimpleName())));
            partnerRepository.save(existingPartner);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deletePartner(Partner partner, Acknowledgment acknowledgment) {
        try {
            partnerRepository.deleteByReference(partner.getReference());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewService(com.datatricks.common.model.Service service, Acknowledgment acknowledgment) {
        try {
            service.setPartner(partnerRepository.findByReference(service.getPartner().getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Partner not found", "Partner", Service.class.getSimpleName())));
            service.setCurrency(currencyRepository.findByCode(service.getCurrency().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Currency not found", "Currency", Service.class.getSimpleName())));
            service.setIntendedFor(staticParameterRepository.findByCode(service.getIntendedFor().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Intended for not found", "Intended for", Service.class.getSimpleName())));
            serviceRepository.save(service);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateService(com.datatricks.common.model.Service service, Acknowledgment acknowledgment) {
        try {
            var existingService = serviceRepository.findByReference(service.getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Service not found", "Service", Service.class.getSimpleName()));
            existingService.setPartner(partnerRepository.findByReference(service.getPartner().getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Partner not found", "Partner", Service.class.getSimpleName())));
            existingService.setCurrency(currencyRepository.findByCode(service.getCurrency().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Currency not found", "Currency", Service.class.getSimpleName())));
            service.setIntendedFor(staticParameterRepository.findByCode(service.getIntendedFor().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Intended for not found", "Intended for", Service.class.getSimpleName())));
            serviceRepository.save(existingService);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteService(com.datatricks.common.model.Service service, Acknowledgment acknowledgment) {
        try {
            serviceRepository.deleteByReference(service.getReference());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewServiceOperation(ServiceOperation serviceOperation, Acknowledgment acknowledgment) {
        try {
            serviceOperation.setService(serviceRepository.findByReference(serviceOperation.getService().getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Service not found", "Service", ServiceOperation.class.getSimpleName())));
            serviceOperation.setOperation(operationRepository.findByCode(serviceOperation.getOperation().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Operation not found", "Operation", ServiceOperation.class.getSimpleName())));
            serviceOperationRepository.save(serviceOperation);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateServiceOperation(ServiceOperation serviceOperation, Acknowledgment acknowledgment) {
        try {
            var existingServiceOperation = serviceOperationRepository.findByReference(serviceOperation.getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Service operation not found", "Service operation", ServiceOperation.class.getSimpleName()));
            existingServiceOperation.setService(serviceRepository.findByReference(serviceOperation.getService().getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Service not found", "Service", ServiceOperation.class.getSimpleName())));
            existingServiceOperation.setOperation(operationRepository.findByCode(serviceOperation.getOperation().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Operation not found", "Operation", ServiceOperation.class.getSimpleName())));
            serviceOperationRepository.save(existingServiceOperation);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteServiceOperation(ServiceOperation serviceOperation, Acknowledgment acknowledgment) {
        try {
            serviceOperationRepository.deleteByServiceReferenceAndOperationCode(serviceOperation.getService().getReference(), serviceOperation.getOperation().getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewServiceProduct(ServiceActivityProduct serviceProduct, Acknowledgment acknowledgment) {
        try {
            serviceProduct.setService(serviceRepository.findByReference(serviceProduct.getService().getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Service not found", "Service", ServiceActivityProduct.class.getSimpleName())));
            serviceProduct.setProduct(productRepository.findByCode(serviceProduct.getProduct().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Product not found", "Product", ServiceActivityProduct.class.getSimpleName())));
            serviceActivityProductRepository.save(serviceProduct);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateServiceProduct(ServiceActivityProduct serviceProduct, Acknowledgment acknowledgment) {
        try {
            var existingServiceProduct = serviceActivityProductRepository.findByReference(serviceProduct.getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Service product not found", "Service product", ServiceActivityProduct.class.getSimpleName()));
            existingServiceProduct.setService(serviceRepository.findByReference(serviceProduct.getService().getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Service not found", "Service", ServiceActivityProduct.class.getSimpleName())));
            existingServiceProduct.setProduct(productRepository.findByCode(serviceProduct.getProduct().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Product not found", "Product", ServiceActivityProduct.class.getSimpleName())));
            serviceActivityProductRepository.save(existingServiceProduct);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteServiceProduct(ServiceActivityProduct serviceProduct, Acknowledgment acknowledgment) {
        try {
            serviceActivityProductRepository.deleteByServiceReferenceAndProductCode(serviceProduct.getService().getReference(), serviceProduct.getProduct().getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewServiceNap(ServiceNap serviceNap, Acknowledgment acknowledgment) {
        try {
            serviceNap.setService(serviceRepository.findByReference(serviceNap.getService().getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Service not found", "Service", ServiceNap.class.getSimpleName())));
            serviceNap.setNap(categoryRepository.findByCode(serviceNap.getNap().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Nap not found", "Nap", ServiceNap.class.getSimpleName())));
            serviceNapRepository.save(serviceNap);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateServiceNap(ServiceNap serviceNap, Acknowledgment acknowledgment) {
        try {
            var existingServiceNap = serviceNapRepository.findByReference(serviceNap.getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Service nap not found" + serviceNap.getReference(), "Service nap", ServiceNap.class.getSimpleName()));
            existingServiceNap.setService(serviceRepository.findByReference(serviceNap.getService().getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Service not found", "Service", ServiceNap.class.getSimpleName())));
            existingServiceNap.setNap(categoryRepository.findByCode(serviceNap.getNap().getCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Nap not found", "Nap", ServiceNap.class.getSimpleName())));
            serviceNapRepository.save(existingServiceNap);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteServiceNap(ServiceNap serviceNap, Acknowledgment acknowledgment) {
        try {
            serviceNapRepository.deleteByServiceReferenceAndNapCode(serviceNap.getService().getReference(), serviceNap.getNap().getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewServiceActor(ServiceActor serviceActor, Acknowledgment acknowledgment) {
        try {
            serviceActor.setService(serviceRepository.findByReference(serviceActor.getService().getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Service not found", "Service", ServiceActor.class.getSimpleName())));
            serviceActor.setActor(actorRepository.findByReference(serviceActor.getActor().getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Actor not found", "Actor", ServiceActor.class.getSimpleName())));
            serviceActorRepository.save(serviceActor);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateServiceActor(ServiceActor serviceActor, Acknowledgment acknowledgment) {
        try {
            var existingServiceActor = serviceActorRepository.findByReference(serviceActor.getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Service actor not found", "Service actor", ServiceActor.class.getSimpleName()));
            existingServiceActor.setService(serviceRepository.findByReference(serviceActor.getService().getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Service not found", "Service", ServiceActor.class.getSimpleName())));
            existingServiceActor.setActor(actorRepository.findByReference(serviceActor.getActor().getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Actor not found", "Actor", ServiceActor.class.getSimpleName())));
            serviceActorRepository.save(existingServiceActor);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteServiceActor(ServiceActor serviceActor, Acknowledgment acknowledgment) {
        try {
            serviceActorRepository.deleteByServiceReferenceAndActorReference(serviceActor.getService().getReference(), serviceActor.getActor().getReference());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewBankBranch(BankBranch bankBranch, Acknowledgment acknowledgment) {
        try {
            bankBranchRepository.save(bankBranch);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateBankBranch(BankBranch bankBranch, Acknowledgment acknowledgment) {
        try {
            var existingBankBranch = bankBranchRepository.findByReferenceAndDeletedAtIsNull(bankBranch.getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Bank branch not found", "Bank branch", BankBranch.class.getSimpleName()));
            existingBankBranch.setCountry(bankBranch.getCountry());
            existingBankBranch.setInterbankCode(bankBranch.getInterbankCode());
            existingBankBranch.setSwift(bankBranch.getSwift());
            existingBankBranch.setCity(bankBranch.getCity());
            existingBankBranch.setPostalCode(bankBranch.getPostalCode());
            existingBankBranch.setAddress(bankBranch.getAddress());
            existingBankBranch.setDomiciliation(bankBranch.getDomiciliation());
            bankBranchRepository.save(existingBankBranch);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    public void deleteBankBranch(BankBranch bankBranch, Acknowledgment acknowledgment) {
        try {
            bankBranchRepository.findByReferenceAndDeletedAtIsNull(bankBranch.getReference()).ifPresentOrElse(
                    b -> {
                        b.setDeletedAt(new Date());
                        bankBranchRepository.save(b);
                    },
                    () -> {
                        throw new ResourcesNotFoundException("Bank branch not found", "Bank branch", BankBranch.class.getSimpleName());
                    }
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }
}
