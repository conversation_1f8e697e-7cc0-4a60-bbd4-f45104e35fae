package com.datatricks.kafkamodule.listener;

import com.datatricks.common.exception.TechnicalException;
import com.datatricks.common.model.*;
import com.datatricks.kafkamodule.model.*;
import com.datatricks.kafkamodule.model.ScaleServiceStreamDto;
import com.datatricks.kafkamodule.service.StaticTablesService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.web.client.RestClient;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@org.springframework.stereotype.Service
public class StaticTablesListener {
    private final ObjectMapper objectMapper;
    private final ModelMapper modelMapper;
    private final StaticTablesService staticTablesService;
    private static final String MODEL_NAME = "StaticTables";
    private static final String ERRORMESSAGE = "Error while processing message ";
    private final Logger logger = org.slf4j.LoggerFactory.getLogger(StaticTablesListener.class);
    private final RestClient.Builder builder;

    public StaticTablesListener(ObjectMapper objectMapper, ModelMapper modelMapper, StaticTablesService staticTablesService, RestClient.Builder builder) {
        this.objectMapper = objectMapper;
        this.modelMapper = modelMapper;
        this.staticTablesService = staticTablesService;
        this.builder = builder;
    }

    @KafkaListener(topics = "${spring.kafka.topic.listenTo.static-tables-topic}"
            , groupId = "${spring.kafka.consumer.group-id}")
    public void listenCreateTopic(String message, Acknowledgment acknowledgment) {
        try {
            KafkaMessage<?> kafkaMessage = objectMapper.readValue(message, KafkaMessage.class);
            switch (kafkaMessage.getClassName()) {
                case "Milestone" -> {
                    var milestone = modelMapper.map(kafkaMessage.getData(), Milestone.class);
                    milestone.setId(null);
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewMilestone(milestone, acknowledgment);
                        case PUT -> staticTablesService.updateMilestone(milestone, acknowledgment);
                        case DELETE -> staticTablesService.deleteMilestone(milestone, acknowledgment);
                    }
                }
                case "Phase" -> {
                    var phase = modelMapper.map(kafkaMessage.getData(), Phase.class);
                    phase.setId(null);
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewPhase(phase, acknowledgment);
                        case PUT -> staticTablesService.updatePhase(phase, acknowledgment);
                        case DELETE -> staticTablesService.deletePhase(phase, acknowledgment);
                    }
                }
                case "LegalCategory" -> {
                    var legalCategory = modelMapper.map(kafkaMessage.getData(), LegalCategory.class);
                    legalCategory.setId(null);
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewLegalCategory(legalCategory, acknowledgment);
                        case PUT -> staticTablesService.updateLegalCategory(legalCategory, acknowledgment);
                        case DELETE -> staticTablesService.deleteLegalCategory(legalCategory, acknowledgment);
                    }
                }
                case "Delegation" -> {
                    var delegation = modelMapper.map(kafkaMessage.getData(), Delegation.class);
                    delegation.setId(null);
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewDelegation(delegation, acknowledgment);
                        case PUT -> staticTablesService.updateDelegation(delegation, acknowledgment);
                        case DELETE -> staticTablesService.deleteDelegation(delegation, acknowledgment);
                    }
                }
                case "Currency" -> {
                    CurrencyStaticTableStreamDto currencyDto = modelMapper.map(kafkaMessage.getData(), CurrencyStaticTableStreamDto.class);
                    var currency = new Currency(currencyDto.getCode(), currencyDto.getLabel(), currencyDto.getActive());
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewCurrency(currency, acknowledgment);
                        case PUT -> staticTablesService.updateCurrency(currency, acknowledgment);
                        case DELETE -> staticTablesService.deleteCurrency(currency, acknowledgment);
                    }
                }
                case "Country" -> {
                    var country = modelMapper.map(kafkaMessage.getData(), Country.class);
                    country.setId(null);
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewCountry(country, acknowledgment);
                        case PUT -> staticTablesService.updateCountry(country, acknowledgment);
                        case DELETE -> staticTablesService.deleteCountry(country, acknowledgment);
                    }
                }
                case "PaymentMethod" -> {
                    var paymentMethodStreamDto = modelMapper.map(kafkaMessage.getData(), PaymentMethodStreamDto.class);
                    PaymentMethod paymentMethod = PaymentMethod.builder()
                            .code(paymentMethodStreamDto.getCode())
                            .label(paymentMethodStreamDto.getLabel())
                            .language(paymentMethodStreamDto.getLanguage())
                            .requiresBankAccount(paymentMethodStreamDto.getRequires_bank_account())
                            .build();
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewPaymentMethod(paymentMethod, acknowledgment);
                        case PUT -> staticTablesService.updatePaymentMethod(paymentMethod, acknowledgment);
                        case DELETE -> staticTablesService.deletePaymentMethod(paymentMethod, acknowledgment);
                    }
                }
                case "Activity" -> {
                    var activityDto = modelMapper.map(kafkaMessage.getData(), ActivityDto.class);
                    var activity = modelMapper.map(activityDto, Activity.class);
                    activity.setId(null);
                    //activity.setAssociatedTo(activityDto.getAssociated_to());
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewActivity(activity, acknowledgment);
                        case PUT -> staticTablesService.updateActivity(activity, acknowledgment);
                        case DELETE -> staticTablesService.deleteActivity(activity, acknowledgment);
                    }
                }
                case "Role" -> {
                    var roleDto = modelMapper.map(kafkaMessage.getData(), RoleStreamDto.class);
                    Role role = new Role(roleDto.getCode(), roleDto.getLabel(), roleDto.getActive());
                    role.setStaticRoleCode(new StaticRole(roleDto.getStatic_role()));
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewRole(role, acknowledgment);
                        case PUT -> staticTablesService.updateRole(role, acknowledgment);
                        case DELETE -> staticTablesService.deleteRole(role, acknowledgment);
                    }
                }
                case "Product" -> {
                    var staticTableProductStreamDto = modelMapper.map(kafkaMessage.getData(), StaticTableProductStreamDto.class);
                    var product = new Product(staticTableProductStreamDto.getLabel(),
                            staticTableProductStreamDto.getCode(), staticTableProductStreamDto.getActivity_code(),
                            staticTableProductStreamDto.getActive());
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewProduct(product, acknowledgment);
                        case PUT -> staticTablesService.updateProduct(product, acknowledgment);
                        case DELETE -> staticTablesService.deleteProduct(product, acknowledgment);
                    }
                }
                case "LineType" -> {
                    var lineType = modelMapper.map(kafkaMessage.getData(), LineType.class);
                    lineType.setId(null);
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewLineType(lineType, acknowledgment);
                        case PUT -> staticTablesService.updateLineType(lineType, acknowledgment);
                        case DELETE -> staticTablesService.deleteLineType(lineType, acknowledgment);
                    }
                }
                case "Operation" -> {
                    var operation = modelMapper.map(kafkaMessage.getData(), Operation.class);
                    operation.setId(null);
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewOperation(operation, acknowledgment);
                        case PUT -> staticTablesService.updateOperation(operation, acknowledgment);
                        case DELETE -> staticTablesService.deleteOperation(operation, acknowledgment);
                    }
                }
                case "Nature" -> {
                    var nature = modelMapper.map(kafkaMessage.getData(), Nature.class);
                    nature.setId(null);
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewNature(nature, acknowledgment);
                        case PUT -> staticTablesService.updateNature(nature, acknowledgment);
                        case DELETE -> staticTablesService.deleteNature(nature, acknowledgment);
                    }
                }
                case "OperationNatureMapping" -> {
                    var operationNatureMappingStreamDto = modelMapper.map(kafkaMessage.getData(), OperationNatureMappingStreamDto.class);
                    var operationNatureMapping = new OperationNatureMapping(
                            operationNatureMappingStreamDto.getOperation_nature_code(),
                            new Operation(operationNatureMappingStreamDto.getOperation_code()),
                            new Nature(operationNatureMappingStreamDto.getNature_code()),
                            operationNatureMappingStreamDto.getNature_status());
                    switch (kafkaMessage.getOperation()) {
                        case POST ->
                                staticTablesService.createNewOperationNatureMapping(operationNatureMapping, acknowledgment);
                        case PUT ->
                                staticTablesService.updateOperationNatureMapping(operationNatureMapping, acknowledgment);
                        case DELETE ->
                                staticTablesService.deleteOperationNatureMapping(operationNatureMapping, acknowledgment);
                    }
                }
                case "OperationNatureTypeMapping" -> {
                    var operationNatureTypeMappingStreamDto = modelMapper.map(kafkaMessage.getData(), OperationNatureTypeMappingStreamDto.class);
                    var operationNatureTypeMapping = new OperationNatureTypeMapping(
                            operationNatureTypeMappingStreamDto.getOperation_nature_type_code(),
                            new OperationNatureMapping(operationNatureTypeMappingStreamDto.getOperation_nature_code()),
                            new LineType(operationNatureTypeMappingStreamDto.getType_code()),
                            operationNatureTypeMappingStreamDto.getType_status());
                    switch (kafkaMessage.getOperation()) {
                        case POST ->
                                staticTablesService.createNewOperationNatureTypeMapping(operationNatureTypeMapping, acknowledgment);
                        case PUT ->
                                staticTablesService.updateOperationNatureTypeMapping(operationNatureTypeMapping, acknowledgment);
                        case DELETE ->
                                staticTablesService.deleteOperationNatureTypeMapping(operationNatureTypeMapping, acknowledgment);
                    }
                }
                case "ProductLineTypeMapping" -> {
                    var productLineTypeMappingStreamDto = modelMapper.map(kafkaMessage.getData(), ProductLineTypeMappingStreamDto.class);
                    var productLineTypeMapping = new ProductLineTypeMapping(
                            new Product(productLineTypeMappingStreamDto.getProduct_code()),
                            new OperationNatureTypeMapping(productLineTypeMappingStreamDto.getOperation_nature_type_code()));
                    switch (kafkaMessage.getOperation()) {
                        case POST ->
                                staticTablesService.createNewProductLineTypeMapping(productLineTypeMapping, acknowledgment);
                        case PUT ->
                                staticTablesService.updateProductLineTypeMapping(productLineTypeMapping, acknowledgment);
                        case DELETE ->
                                staticTablesService.deleteProductLineTypeMapping(productLineTypeMapping, acknowledgment);
                    }
                }
                case "Tax" -> {
                    var taxStreamDto = modelMapper.map(kafkaMessage.getData(), TaxStreamDto.class);
                    var tax = new Tax(taxStreamDto.getCode(), taxStreamDto.getLabel(), taxStreamDto.getStart_date(),
                            taxStreamDto.getEnd_date(), taxStreamDto.getCountry(), taxStreamDto.getActive(), taxStreamDto.getSystemAttribute());
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewTax(tax, acknowledgment);
                        case PUT -> staticTablesService.updateTax(tax, acknowledgment);
                        case DELETE -> staticTablesService.deleteTax(tax, acknowledgment);
                    }
                }
                case "TaxRate" -> {
                    var taxRateStreamDto = modelMapper.map(kafkaMessage.getData(), TaxRateStreamDto.class);
                    var taxRate = new TaxRate(new Tax(taxRateStreamDto.getTax_code()),
                            taxRateStreamDto.getRate(), taxRateStreamDto.getStart_date(), taxRateStreamDto.getEnd_date(),
                            taxRateStreamDto.getActive(), taxRateStreamDto.getSystemAttribute(), taxRateStreamDto.getCode(),
                            taxRateStreamDto.getLabel());
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewTaxRate(taxRate, acknowledgment);
                        case PUT -> staticTablesService.updateTaxRate(taxRate, acknowledgment);
                        case DELETE -> staticTablesService.deleteTaxRate(taxRate, acknowledgment);
                    }
                }
                case "Naf" -> {
                    LegalActivities legalActivity = modelMapper.map(kafkaMessage.getData(), LegalActivities.class);
                    legalActivity.setId(null);
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createLegalActivity(legalActivity, acknowledgment);
                        case PUT -> staticTablesService.updateLegalActivity(legalActivity, acknowledgment);
                        case DELETE -> staticTablesService.deleteLegalActivity(legalActivity, acknowledgment);
                    }
                }
                case "Nap" -> {
                    Category category = modelMapper.map(kafkaMessage.getData(), Category.class);
                    category.setId(null);
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createCategory(category, acknowledgment);
                        case PUT -> staticTablesService.updateCategory(category, acknowledgment);
                        case DELETE -> staticTablesService.deleteCategory(category, acknowledgment);
                    }
                }
                case "Market" -> {
                    Market market = modelMapper.map(kafkaMessage.getData(), Market.class);
                    market.setId(null);
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createMarket(market, acknowledgment);
                        case PUT -> staticTablesService.updateMarket(market, acknowledgment);
                        case DELETE -> staticTablesService.deleteMarket(market, acknowledgment);
                    }
                }
                case "Equipment" -> {
                    EquipmentStreamDto equipmentStreamDto = modelMapper.map(kafkaMessage.getData(), EquipmentStreamDto.class);
                    var equipment = new Equipment(equipmentStreamDto.getCode(),
                            equipmentStreamDto.getLabel(), equipmentStreamDto.getDescription(),
                            equipmentStreamDto.getSystem_attribute(), equipmentStreamDto.getActive(),
                            new Market(equipmentStreamDto.getMarket_code()), new Category(equipmentStreamDto.getCategory_code()),
                            new Country(equipmentStreamDto.getCountry_code()));
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewEquipment(equipment, acknowledgment);
                        case PUT -> staticTablesService.updateEquipment(equipment, acknowledgment);
                        case DELETE -> staticTablesService.deleteEquipment(equipment, acknowledgment);
                    }
                }
                case "Scale" -> {
                    ScaleStreamDto scaleStreamDto = modelMapper.map(kafkaMessage.getData(), ScaleStreamDto.class);
                    // Extract date fields from the original data map
                    if (kafkaMessage.getData() instanceof Map<?, ?> dataMap) {
                        if (dataMap.containsKey("start_date") && dataMap.get("start_date") != null) {
                            scaleStreamDto.setStart_date(LocalDate.parse(dataMap.get("start_date").toString()));
                        }
                        if (dataMap.containsKey("end_date") && dataMap.get("end_date") != null) {
                            scaleStreamDto.setEnd_date(LocalDate.parse(dataMap.get("end_date").toString()));
                        }
                    }
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewScale(scaleStreamDto, acknowledgment);
                        case PUT -> staticTablesService.updateScale(scaleStreamDto, acknowledgment);
                        case DELETE -> staticTablesService.deleteScale(scaleStreamDto, acknowledgment);
                    }
                }
                case "ApplicationCriteria" -> {
                    ApplicationCriteriaStreamDto applicationCriteriaStreamDto = modelMapper.map(kafkaMessage.getData(),
                            ApplicationCriteriaStreamDto.class);
                    ApplicationCriteria applicationCriteria = ApplicationCriteria.builder()
                            .reference(UUID.fromString(applicationCriteriaStreamDto.getReference()))
                            .channelOfAcquisition(applicationCriteriaStreamDto.getChannel_of_acquisition())
                            .currency(new Currency(applicationCriteriaStreamDto.getCurrency_code()))
                            .customerType(applicationCriteriaStreamDto.getCustomer_type())
                            .financialScoring(applicationCriteriaStreamDto.getFinancial_scoring())
                            .scale(new Scale(applicationCriteriaStreamDto.getScale_code()))
                            .build();
                    switch (kafkaMessage.getOperation()) {
                        case POST ->
                                staticTablesService.createNewApplicationCriteria(applicationCriteria, acknowledgment);
                        case PUT -> staticTablesService.updateApplicationCriteria(applicationCriteria, acknowledgment);
                        case DELETE ->
                                staticTablesService.deleteApplicationCriteria(applicationCriteria, acknowledgment);
                    }
                }
                case "ApplicationCriteriaActor" -> {
                    ApplicationCriteriaActorStreamDto applicationCriteriaActorStreamDto = modelMapper.map(kafkaMessage.getData(),
                            ApplicationCriteriaActorStreamDto.class);
                    ApplicationCriteriaActors applicationCriteriaActors = ApplicationCriteriaActors.builder()
                            .reference(UUID.fromString(applicationCriteriaActorStreamDto.getReference()))
                            .applicationCriteria(new ApplicationCriteria(applicationCriteriaActorStreamDto.getApplication_criteria_reference()))
                            .actor(new Actor(applicationCriteriaActorStreamDto.getActor_reference()))
                            .build();
                    switch (kafkaMessage.getOperation()) {
                        case POST ->
                                staticTablesService.createNewApplicationCriteriaActor(applicationCriteriaActors, acknowledgment);
                        case PUT ->
                                staticTablesService.updateApplicationCriteriaActor(applicationCriteriaActors, acknowledgment);
                        case DELETE ->
                                staticTablesService.deleteApplicationCriteriaActor(applicationCriteriaActors, acknowledgment);
                    }
                }
                case "ScaleFinancialProduct" -> {
                    ScaleFinancialProductStreamDto scaleFinancialProductStreamDto = modelMapper.map(kafkaMessage.getData(),
                            ScaleFinancialProductStreamDto.class);
                    var scaleFinancialProduct = new ScaleFinancialProduct(scaleFinancialProductStreamDto.getReference(),
                            new Scale(scaleFinancialProductStreamDto.getScale_code()),
                            new Product(scaleFinancialProductStreamDto.getProduct_code()));
                    switch (kafkaMessage.getOperation()) {
                        case POST ->
                                staticTablesService.createNewScaleFinancialProduct(scaleFinancialProduct, acknowledgment);
                        case PUT ->
                                staticTablesService.updateScaleFinancialProduct(scaleFinancialProduct, acknowledgment);
                        case DELETE ->
                                staticTablesService.deleteScaleFinancialProduct(scaleFinancialProduct, acknowledgment);
                    }
                }
                case "FinancialElementMaterialEquipmentMapping" -> {
                    FinancialElementMaterialEquipmentMappingStreamDto financialElementMaterialEquipmentMappingStreamDto = modelMapper.map(kafkaMessage.getData(),
                            FinancialElementMaterialEquipmentMappingStreamDto.class);
                    var financialElementMaterialEquipmentMapping = new ScaleElementMaterialEquipment(
                            financialElementMaterialEquipmentMappingStreamDto.getReference(),
                            new ScaleElementMaterial(financialElementMaterialEquipmentMappingStreamDto.getScale_code()),
                            new Equipment(financialElementMaterialEquipmentMappingStreamDto.getEquipment_code()));
                    switch (kafkaMessage.getOperation()) {
                        case POST ->
                                staticTablesService.createNewFinancialElementMaterialEquipmentMapping(financialElementMaterialEquipmentMapping, acknowledgment);
                        case PUT ->
                                staticTablesService.updateFinancialElementMaterialEquipmentMapping(financialElementMaterialEquipmentMapping, acknowledgment);
                        case DELETE ->
                                staticTablesService.deleteFinancialElementMaterialEquipmentMapping(financialElementMaterialEquipmentMapping, acknowledgment);
                    }
                }
                case "FinancialElementVehicleVehicleMapping" -> {
                    FinancialElementVehicleVehicleMappingStreamDto financialElementVehicleVehicleMappingStreamDto = modelMapper.map(kafkaMessage.getData(),
                            FinancialElementVehicleVehicleMappingStreamDto.class);
                    var financialElementVehicleVehicleMapping = new ScaleElementVehicleVehicle(
                            financialElementVehicleVehicleMappingStreamDto.getReference(),
                            new ScaleElementVehicle(financialElementVehicleVehicleMappingStreamDto.getScale_code()),
                            new VehicleStock(financialElementVehicleVehicleMappingStreamDto.getVehicle_reference()));
                    switch (kafkaMessage.getOperation()) {
                        case POST ->
                                staticTablesService.createNewFinancialElementVehicleVehicleMapping(financialElementVehicleVehicleMapping, acknowledgment);
                        case PUT ->
                                staticTablesService.updateFinancialElementVehicleVehicleMapping(financialElementVehicleVehicleMapping, acknowledgment);
                        case DELETE ->
                                staticTablesService.deleteFinancialElementVehicleVehicleMapping(financialElementVehicleVehicleMapping, acknowledgment);
                    }
                }
                case "ThirdParties" -> {
                    ThirdPartiesStreamDto thirdPartiesStreamDto = modelMapper.map(kafkaMessage.getData(), ThirdPartiesStreamDto.class);
                    var thirdParties = new ThirdParty(
                            thirdPartiesStreamDto.getReference(), new Actor(thirdPartiesStreamDto.getActor_reference()),
                            new Scale(thirdPartiesStreamDto.getScale_code()), new Role(thirdPartiesStreamDto.getRole_code())
                    );
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewThirdParties(thirdParties, acknowledgment);
                        case PUT -> staticTablesService.updateThirdParties(thirdParties, acknowledgment);
                        case DELETE -> staticTablesService.deleteThirdParties(thirdParties, acknowledgment);
                    }
                }
                case "ScaleService" -> {
                    ScaleServiceStreamDto scaleServiceStreamDto = modelMapper.map(kafkaMessage.getData(), ScaleServiceStreamDto.class);
                    var scaleService = new ScaleService(scaleServiceStreamDto.getReference(),
                            new Scale(scaleServiceStreamDto.getScale_code()),
                            new Service(scaleServiceStreamDto.getService_reference()));

                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createScaleService(scaleService, acknowledgment);
                        case PUT -> staticTablesService.updateScaleService(scaleService, acknowledgment);
                        case DELETE -> staticTablesService.deleteScaleService(scaleService, acknowledgment);
                    }
                }
                case "Vehicle" -> {
                    try {
                        List<VehicleStreamDto> vehicleStreamDtos = objectMapper.convertValue(
                                kafkaMessage.getData(),
                                objectMapper.getTypeFactory().constructCollectionType(List.class, VehicleStreamDto.class)
                        );

                        for (VehicleStreamDto vehicleDto : vehicleStreamDtos) {
                            var vehicleStock = new VehicleStock(vehicleDto.getReference(),
                                    vehicleDto.getBody_label(),
                                    vehicleDto.getModel_group_label(),
                                    vehicleDto.getVersion_label() + " - " + vehicleDto.getVariant_label(),
                                    vehicleDto.getEnergy_code() + " - " + vehicleDto.getEnergy_code() + " - " + vehicleDto.getPower(),
                                    vehicleDto.getBody_code() + " - " + vehicleDto.getBody_label(),
                                    0, 0, vehicleDto.getPublic_price_incl_tax(), 0, null);

                            switch (kafkaMessage.getOperation()) {
                                case POST -> staticTablesService.createNewVehicle(vehicleStock, acknowledgment);
                                case PUT -> staticTablesService.updateVehicle(vehicleStock, acknowledgment);
                                case DELETE -> staticTablesService.deleteVehicle(vehicleStock, acknowledgment);
                            }
                        }
                    } catch (IllegalArgumentException e) {
                        // Handle case of single vehicle
                        VehicleStreamDto vehicleStockStreamDto = modelMapper.map(kafkaMessage.getData(), VehicleStreamDto.class);
                        var vehicleStock = new VehicleStock(vehicleStockStreamDto.getReference(),
                                vehicleStockStreamDto.getBody_label(),
                                vehicleStockStreamDto.getModel_group_label(),
                                vehicleStockStreamDto.getVersion_label() + " - " + vehicleStockStreamDto.getVariant_label(),
                                vehicleStockStreamDto.getEnergy_code() + " - " + vehicleStockStreamDto.getEnergy_code() + " - " + vehicleStockStreamDto.getPower(),
                                vehicleStockStreamDto.getBody_code() + " - " + vehicleStockStreamDto.getBody_label(),
                                0, 0, 0, 0, null);

                        switch (kafkaMessage.getOperation()) {
                            case POST -> staticTablesService.createNewVehicle(vehicleStock, acknowledgment);
                            case PUT -> staticTablesService.updateVehicle(vehicleStock, acknowledgment);
                            case DELETE -> staticTablesService.deleteVehicle(vehicleStock, acknowledgment);
                        }
                    }
                }
                case "Partner" -> {
                    PartnerStreamDto partnerStreamDto = modelMapper.map(kafkaMessage.getData(), PartnerStreamDto.class);
                    var partner = new Partner(partnerStreamDto.getReference(), partnerStreamDto.getActor_reference(),
                            partnerStreamDto.getOperation_nature_type_code(), partnerStreamDto.getCurrency_code(),
                            partnerStreamDto.getMandate_type(), partnerStreamDto.getExternal_reference(),
                            partnerStreamDto.getRemittance_method(), partnerStreamDto.getCalculation_method(),
                            partnerStreamDto.getAmount_excl_tax(), partnerStreamDto.getBasis_of_calculation(),
                            partnerStreamDto.getTax_code(), partnerStreamDto.getCalculation_percentage(),
                            partnerStreamDto.getTax_value());
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewPartner(partner, acknowledgment);
                        case PUT -> staticTablesService.updatePartner(partner, acknowledgment);
                        case DELETE -> staticTablesService.deletePartner(partner, acknowledgment);
                    }
                }
                case "Service" -> {
                    ServiceStreamDto serviceStreamDto = modelMapper.map(kafkaMessage.getData(), ServiceStreamDto.class);
                    var service = new Service(serviceStreamDto.getReference(), serviceStreamDto.getIntended_for(),
                            serviceStreamDto.getLabel(), serviceStreamDto.getType_of_service(),
                            serviceStreamDto.getType_of_cover(), serviceStreamDto.getStart_date(),
                            serviceStreamDto.getEnd_date(), serviceStreamDto.getCurrency_code(),
                            serviceStreamDto.getStatus(), serviceStreamDto.getOut_of_contract_termination(),
                            serviceStreamDto.getMaximum_asset_price(), serviceStreamDto.getMinimum_asset_price(),
                            serviceStreamDto.getMaximum_contract_duration(), serviceStreamDto.getMinimum_contract_duration(),
                            serviceStreamDto.getIs_enterprise(), serviceStreamDto.getIs_enterprise_individuelle(),
                            serviceStreamDto.getIs_enterprise_publique(), serviceStreamDto.getIs_particulier(),
                            serviceStreamDto.getIs_registrable(), serviceStreamDto.getIs_unregistrable(),
                            serviceStreamDto.getBilling_method(), serviceStreamDto.getCalculation_method(),
                            serviceStreamDto.getAmount_excl_tax(), serviceStreamDto.getBasis_of_calculation(),
                            serviceStreamDto.getTax_code(), serviceStreamDto.getCalculation_percentage(),
                            serviceStreamDto.getTax_value(), serviceStreamDto.getPartner_reference(), serviceStreamDto.getCode());
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewService(service, acknowledgment);
                        case PUT -> staticTablesService.updateService(service, acknowledgment);
                        case DELETE -> staticTablesService.deleteService(service, acknowledgment);
                    }
                }
                case "ServiceOperation" -> {
                    ServiceOperationStreamDto serviceOperationStreamDto = modelMapper.map(kafkaMessage.getData(), ServiceOperationStreamDto.class);
                    var serviceOperation = new ServiceOperation(serviceOperationStreamDto.getReference(),
                            serviceOperationStreamDto.getService_reference(), serviceOperationStreamDto.getOperation_code());
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewServiceOperation(serviceOperation, acknowledgment);
                        case PUT -> staticTablesService.updateServiceOperation(serviceOperation, acknowledgment);
                        case DELETE -> staticTablesService.deleteServiceOperation(serviceOperation, acknowledgment);
                    }
                }
                case "ServiceProduct" -> {
                    ServiceProductStreamDto serviceProductStreamDto = modelMapper.map(kafkaMessage.getData(), ServiceProductStreamDto.class);
                    var serviceProduct = new ServiceActivityProduct(serviceProductStreamDto.getReference(),
                            serviceProductStreamDto.getService_reference(), serviceProductStreamDto.getProduct_code());
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewServiceProduct(serviceProduct, acknowledgment);
                        case PUT -> staticTablesService.updateServiceProduct(serviceProduct, acknowledgment);
                        case DELETE -> staticTablesService.deleteServiceProduct(serviceProduct, acknowledgment);
                    }
                }
                case "ServiceNap" -> {
                    ServiceNapStreamDto serviceNapStreamDto = modelMapper.map(kafkaMessage.getData(), ServiceNapStreamDto.class);
                    var serviceNap = new ServiceNap(serviceNapStreamDto.getReference(),
                            serviceNapStreamDto.getService_reference(), serviceNapStreamDto.getNap_code());
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewServiceNap(serviceNap, acknowledgment);
                        case PUT -> staticTablesService.updateServiceNap(serviceNap, acknowledgment);
                        case DELETE -> staticTablesService.deleteServiceNap(serviceNap, acknowledgment);
                    }
                }
                case "ServiceActor" -> {
                    ServiceActorStreamDto serviceActorStreamDto = modelMapper.map(kafkaMessage.getData(), ServiceActorStreamDto.class);
                    var serviceActor = new ServiceActor(serviceActorStreamDto.getReference(),
                            serviceActorStreamDto.getService_reference(), serviceActorStreamDto.getActor_code());
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewServiceActor(serviceActor, acknowledgment);
                        case PUT -> staticTablesService.updateServiceActor(serviceActor, acknowledgment);
                        case DELETE -> staticTablesService.deleteServiceActor(serviceActor, acknowledgment);
                    }
                }
                case "BankBranch" -> {
                    BankStreamDto bankStreamDto = modelMapper.map(kafkaMessage.getData(), BankStreamDto.class);
                    var bankBranch = new BankBranch(bankStreamDto.getReference(),
                            bankStreamDto.getCountry_code(), bankStreamDto.getBank_id(),
                            bankStreamDto.getBank_name(), bankStreamDto.getCode_swift(), bankStreamDto.getCode_branch(),
                            bankStreamDto.getCity(), bankStreamDto.getPostal_code(), bankStreamDto.getAddress(),
                            bankStreamDto.getSecond_address());
                    switch (kafkaMessage.getOperation()) {
                        case POST -> staticTablesService.createNewBankBranch(bankBranch, acknowledgment);
                        case PUT -> staticTablesService.updateBankBranch(bankBranch, acknowledgment);
                        case DELETE -> staticTablesService.deleteBankBranch(bankBranch, acknowledgment);
                    }
                }
                default -> {
                    break;
                }
            }
            acknowledgment.acknowledge();
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }
}
