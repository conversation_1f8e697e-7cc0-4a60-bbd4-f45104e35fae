package com.datatricks.actors.controller;

import com.datatricks.common.exception.handler.InformativeMessage;
import com.datatricks.actors.service.ActorActivityProductService;
import com.datatricks.common.model.dto.ActorActivityProductDTO;
import com.datatricks.common.model.dto.PageDto;
import com.datatricks.common.model.dto.PatchActorActivityProductDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
@AllArgsConstructor
@Tag(name = "Actor Activity Product", description = "Actor Activity Product API")
public class ActorActivityProductController {
    private final ActorActivityProductService actorActivityProductService;

    @GetMapping("/v1/actors/{actor_reference}/actor-activity-products")
    public ResponseEntity<PageDto<ActorActivityProductDTO>> getActorActivityProducts(@PathVariable(name = "actor_reference") String actorReference) {
        return actorActivityProductService.getActorActivityProducts(actorReference);
    }

    @PostMapping("/v1/actors/{actor_reference}/actor-activity-products")
    public ResponseEntity<PageDto<ActorActivityProductDTO>> createActorActivityProduct(@PathVariable(name = "actor_reference") String actorReference,
                                                                                       @RequestBody ActorActivityProductDTO actorActivityProductDTO) {
        return actorActivityProductService.saveActorActivityProducts(actorReference, actorActivityProductDTO);
    }

    @PatchMapping("/v1/actors/{actor_reference}/actor-activity-products")
    public ResponseEntity<PageDto<ActorActivityProductDTO>> updateActorActivityProduct(@PathVariable(name = "actor_reference") String actorReference,
                                                                                       @RequestBody PatchActorActivityProductDTO actorActivityProductDTO) {
        return actorActivityProductService.patchActorActivityProductsByActorReference(actorReference, actorActivityProductDTO);
    }

    @DeleteMapping("/v1/actors/{actor_reference}/actor-activity-products/{activity_code}")
    public ResponseEntity<InformativeMessage> deleteActorActivityProduct(@PathVariable(name = "actor_reference") String actorReference,
                                                                         @PathVariable(name = "activity_code") String activityCode) {
        return actorActivityProductService.deleteActorActivityProducts(actorReference, activityCode);
    }

    @DeleteMapping("/v1/actors/{actor_reference}/actor-activity-products/{activity_code}/{product_code}")
    public ResponseEntity<InformativeMessage> deleteActorActivityProductByProductCode(@PathVariable(name = "actor_reference") String actorReference,
                                                                                       @PathVariable(name = "activity_code") String activityCode,
                                                                                       @PathVariable(name = "product_code") String productCode) {
        return actorActivityProductService.deleteActorActivityProductsByProductCode(actorReference, activityCode, productCode);
    }

    @PostMapping("/v1/actors/actor-activity-products/{activity_code}/add-new-product")
    public ResponseEntity<InformativeMessage> patchActorActivityAddNewProducts(@PathVariable(name = "activity_code") String activityCode) {
        return actorActivityProductService.patchActorActivityAddNewProducts(activityCode);
    }

    @PutMapping("/v1/actors/actor-activity-products")
    public ResponseEntity<InformativeMessage> updateActorActivityProduct(
            @RequestBody PatchActorActivityProductDTO actorActivityProductDTO) {
        return actorActivityProductService.patchActorActivityProducts(actorActivityProductDTO);
    }

    @DeleteMapping("/v1/actors/actor-activity-products/delete-all-actor-activity-products/{activity_code}/{product_code}")
    public ResponseEntity<InformativeMessage> deleteAllActorActivityProductsByActivityCodeAndProductCode(
            @PathVariable(name = "activity_code") String activityCode,
            @PathVariable(name = "product_code") String productCode) {
        return actorActivityProductService.deleteAllActorActivityProductsByActivityCodeAndProductCode(activityCode, productCode);
    }
}
