package com.datatricks.actors.controller;

import com.datatricks.common.exception.handler.InformativeMessage;
import com.datatricks.actors.service.ContactService;
import com.datatricks.common.model.dto.*;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.Explode;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.enums.ParameterStyle;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

import java.util.ArrayList;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.datatricks.actors.utils.ActorsUtils.handleException;

@RestController
@RequestMapping("/api")
@Tag(name = "Contacts", description = "Contacts API")
public class ContactController {

    private final ContactService contactService;

    @Autowired
    ContactController(ContactService contactService) {
        this.contactService = contactService;
    }

    @Cacheable(value = "contactList", key = "#id + ':' + #params.toString()")
    @GetMapping(value = "/v1/actors/{id}/contacts")
    public ResponseEntity<PageDto<ContactDto>> getActorContacts(@PathVariable Long id, @Parameter(name = "params",
            in = ParameterIn.QUERY,
            schema = @Schema(type = "object", implementation = ContactDto.class),
            style = ParameterStyle.FORM,
            explode = Explode.TRUE) @RequestParam Map<String, String> params) {
        try {
            return contactService.getActorContacts(id, params);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatusCode.valueOf(200)).body(PageDto.<ContactDto>builder()
                    .data(new ArrayList<>())
                    .total(0).build());
        }
    }

    @Cacheable(value = "contactListById", key = "#actorId + ':' + #contactId")
    @GetMapping(value = "/v1/actors/{actor_id}/contacts/{contact_id}")
    public ResponseEntity<SingleResultDto<ContactDto>> getActorContactById(@PathVariable("actor_id") Long actorId, @PathVariable("contact_id") Long contactId) {
        try {
            return contactService.getActorContactById(actorId, contactId);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Caching(
            put = {
                    @CachePut(value = "contactListById", key = "#id + ':' + #result.body.data.id")
            },
            evict = {
                    @CacheEvict(value = "contactList", allEntries = true)
            }
    )
    @PostMapping(value = "/v1/actors/{id}/contacts")
    public ResponseEntity<SingleResultDto<ContactDto>> createContact(@PathVariable Long id, @RequestBody @Valid NewContactDto contact) {
        try {
            return contactService.createNewContact(id, contact);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Caching(
            evict = {
                    @CacheEvict(value = "contactListById", key = "#actorId + ':' + #contactId"),
                    @CacheEvict(value = "contactList", allEntries = true)
            }
    )
    @PutMapping(value = "/v1/actors/{actor_id}/contacts/{contact_id}")
    public ResponseEntity<SingleResultDto<ContactDto>> updateContact(
            @PathVariable("actor_id") Long actorId, @PathVariable("contact_id") Long contactId, @RequestBody @Valid NewContactDto newContact) {
        try {
            return contactService.updateExistingContact(actorId, contactId, newContact);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Caching(
            evict = {
                    @CacheEvict(value = "contactListById", key = "#actorId + ':' + #contactId"),
                    @CacheEvict(value = "contactList", allEntries = true)
            }
    )
    @DeleteMapping(value = "/v1/actors/{actor_id}/contacts/{contact_id}")
    public ResponseEntity<InformativeMessage> deleteContactById(@PathVariable("actor_id") Long actorId, @PathVariable("contact_id") Long contactId) {
        try {
            contactService.deleteContactById(actorId, contactId);
            return ResponseEntity.ok(new InformativeMessage("Resource with ID " + contactId + " has been deleted successfully"));

        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @GetMapping("/v1/actors/positions")
    public ResponseEntity<PageDto<PositionDto>> getAllPositions(

            @Parameter(name = "params",
                    in = ParameterIn.QUERY,
                    schema = @Schema(type = "object", implementation = PositionParam.class),
                    style = ParameterStyle.FORM,
                    explode = Explode.TRUE)
            @RequestParam Map<String, String> params) {
        return contactService.getAllPositions(params);
    }

}
