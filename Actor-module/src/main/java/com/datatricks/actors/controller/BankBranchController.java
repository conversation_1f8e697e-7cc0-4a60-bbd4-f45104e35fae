package com.datatricks.actors.controller;

import com.datatricks.common.exception.ResourcesNotFoundException;
import com.datatricks.common.exception.TechnicalException;
import com.datatricks.common.exception.handler.InformativeMessage;
import com.datatricks.actors.service.BankService;
import com.datatricks.common.model.BankBranch;
import com.datatricks.common.model.dto.BankBranchDto;
import com.datatricks.common.model.dto.SingleResultDto;
import com.datatricks.common.repository.BankBranchRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

import java.util.Date;
import java.util.Optional;

import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.datatricks.actors.utils.ActorsUtils.handleException;

@RestController
@RequestMapping("/api")
@Tag(name = "Bank Branch", description = "Bank Branch API")
public class BankBranchController {

    private final BankBranchRepository bankBranchRepository;
    private final ModelMapper modelMapper;
    private final BankService bankService;

    @Autowired
    BankBranchController(BankBranchRepository bankBranchRepository, ModelMapper modelMapper, BankService bankService) {
        this.bankBranchRepository = bankBranchRepository;
        this.modelMapper = modelMapper;
        this.bankService = bankService;
    }

    @Cacheable(value = "bankAccountsBranches", key = "#iban", unless = "#result.body.data == null")
    @GetMapping(value = "/v1/bank-account/branch/{inter_bank_code}")
    public ResponseEntity<SingleResultDto<BankBranchDto>> getBranchInfos(@PathVariable("inter_bank_code") String iban) throws JsonProcessingException {
        return bankService.getBankBranchByInterbankCode(iban);
    }

    @Caching(
            put = {
                    @CachePut(
                            value = "bankAccountsBranches",
                            key = "#result.body.data.id",
                            unless = "#result.statusCode.value() == 404"
                    )
            },
            evict = {
                    @CacheEvict(value = "bankAccountsBranchesByIban", allEntries = true)
            }
    )
    @PostMapping(value = "/v1/bank-account/branch")
    public ResponseEntity<SingleResultDto<BankBranchDto>> saveBranchInfos(@RequestBody @Valid BankBranchDto bankBranch) {
        try {
            var newBankBranch = modelMapper.map(bankBranch, BankBranch.class);
            newBankBranch.setCreatedAt(new Date());
            newBankBranch.setId(null);
            BankBranch createdBranch = bankBranchRepository.save(newBankBranch);
            return ResponseEntity.ok().body(SingleResultDto.<BankBranchDto>builder()
                    .data(modelMapper.map(createdBranch, BankBranchDto.class))
                    .build());
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @Caching(evict = {
            @CacheEvict(value = "bankAccountsBranches", key = "#id"),
            @CacheEvict(value = "bankAccountsBranchesByIban", allEntries = true)
    })
    @PutMapping(value = "/v1/bank-account/branch/{id}")
    public ResponseEntity<SingleResultDto<BankBranchDto>> saveBranchInfos(
            @PathVariable Long id, @RequestBody @Valid BankBranchDto bankBranch) {
        try {
            BankBranch storedBankBranch = bankBranchRepository
                    .findById(id)
                    .orElseThrow(() -> new ResourcesNotFoundException("Branch not found", "BANK_BRANCH", "bank_branch"));
            storedBankBranch.setModifiedAt(new Date());
            storedBankBranch.setInterbankCode(storedBankBranch.getInterbankCode());
            storedBankBranch.setCountry(storedBankBranch.getCountry());
            storedBankBranch.setCity(storedBankBranch.getCity());
            storedBankBranch.setAddress(storedBankBranch.getAddress());
            storedBankBranch.setRegion(storedBankBranch.getRegion());
            storedBankBranch.setDomiciliation(storedBankBranch.getDomiciliation());
            storedBankBranch.setPostalCode(storedBankBranch.getPostalCode());
            storedBankBranch.setSwift(storedBankBranch.getSwift());
            bankBranchRepository.save(storedBankBranch);
            return ResponseEntity.ok().body(SingleResultDto.<BankBranchDto>builder()
                    .data(modelMapper.map(bankBranch, BankBranchDto.class))
                    .build());
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @Caching(evict = {
            @CacheEvict(value = "bankAccountsBranches", key = "#id"),
            @CacheEvict(value = "bankAccountsBranchesByIban", allEntries = true)
    })
    @DeleteMapping(value = "/v1/bank-account/branch/{id}")
    public ResponseEntity<InformativeMessage> deleteBranchInfos(@PathVariable Long id) {
        Optional<BankBranch> storedBankBranch = bankBranchRepository.findById(id);
        if (storedBankBranch.isEmpty()) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        storedBankBranch.get().setDeletedAt(new Date());
        bankBranchRepository.save(storedBankBranch.get());
        return ResponseEntity.ok(new InformativeMessage("Resource with ID " + id + " has been deleted successfully"));
    }

    @Cacheable(value = "bankAccountsBranchesByIban", key = "#iban", unless = "#result.statusCode.value() == 404")
    @GetMapping(value = "/v1/bank-account/branch/search/{iban}")
    public ResponseEntity<SingleResultDto<BankBranchDto>> getNewBranchInfos(@PathVariable String iban) throws TechnicalException {
        try {
            return bankService.validateIban(iban);
        } catch (JsonProcessingException e) {
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
