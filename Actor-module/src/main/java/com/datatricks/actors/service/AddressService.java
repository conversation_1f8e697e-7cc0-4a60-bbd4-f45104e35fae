package com.datatricks.actors.service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

import com.datatricks.actors.model.response.SimulationActorAddressResponse;
import com.datatricks.common.model.Actor;
import com.datatricks.common.model.Address;
import com.datatricks.common.model.PhysicalAddress;
import com.datatricks.common.model.dto.AddressDto;
import com.datatricks.common.model.dto.SingleResultDto;
import com.datatricks.common.repository.ActorRepository;
import com.datatricks.common.repository.AddressRepository;
import com.datatricks.common.repository.PhysicalAdressRepository;
import org.modelmapper.ModelMapper;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.datatricks.common.exception.ConflictException;
import com.datatricks.common.exception.ResourcesNotFoundException;
import com.datatricks.common.exception.handler.InformativeMessage;
import com.datatricks.actors.utils.CustomIdentifierGenerator;

import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Value;

@Service
public class AddressService {

    private static final String ADDRESS_PREFIX = "ADDR_";
    private static final String MODULE = "ADDRESS";
    private final AddressRepository addressRepository;
    private final ActorRepository actorRepository;
    private final PhysicalAdressRepository physicalAdressRepository;
    private final ModelMapper modelMapper;
    private final RestTemplate restTemplate;
    private static final String ADDRESS_NOT_FOUND = "Address not found";

    @Value("${spring.services.offer.url}")
    private String offerServiceUrl;

    public AddressService(
            AddressRepository addressRepository,
            ActorRepository actorRepository,
            PhysicalAdressRepository physicalAdressRepository,
            ModelMapper modelMapper, RestTemplate restTemplate) {
        this.addressRepository = addressRepository;
        this.actorRepository = actorRepository;
        this.physicalAdressRepository = physicalAdressRepository;
        this.modelMapper = modelMapper;
        this.restTemplate = restTemplate;
    }

    @Transactional
    public ResponseEntity<SingleResultDto<AddressDto>> getActorAddressById(Long actorId, Long addressId) {
        if (!actorRepository.existsByIdAndDeletedAtIsNull(actorId)) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        Address address = addressRepository.findByActorIdAndIdAndDeletedAtIsNull(actorId, addressId);

        if (address != null) {
            AddressDto addressDto = this.modelMapper.map(address, AddressDto.class);
            return ResponseEntity.ok(SingleResultDto.<AddressDto>builder().data(addressDto).build());
        } else {
            throw new ResourcesNotFoundException(ADDRESS_NOT_FOUND, "code", MODULE);
        }
    }

    @Transactional
    public ResponseEntity<SingleResultDto<AddressDto>> createAddress(Long id, AddressDto addressDto) {

        Optional<Actor> actorOptional = actorRepository.findByIdAndDeletedAtIsNull(id);
        if (actorOptional.isPresent()) {
            addressDto.setId(null);
            Actor actor = actorOptional.get();
            Address address = modelMapper.map(addressDto, Address.class);
	        //checkDates(address.getStartDate(), address.getEndDate(), address);
            // Set the actor for the address
            address.setActor(actor);

            // Save the physical address
            PhysicalAddress physicalAddress = modelMapper.map(addressDto.getPhysicalAddress(), PhysicalAddress.class);
            address.setReference(CustomIdentifierGenerator.generate(ADDRESS_PREFIX));
            address.setPhysicalAdress(physicalAddress);
            if (addressDto.getSummary() == null) {
                address.setSummary(generateSummary(addressDto));
            } else {
                address.setSummary(addressDto.getSummary());
            }
            // Save the address
            physicalAdressRepository.save(physicalAddress);
            Address createdAddress = addressRepository.save(address);

            // Return the created address
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(SingleResultDto.<AddressDto>builder()
                            .data(modelMapper.map(createdAddress, AddressDto.class))
                            .build());
        } else {
            throw new ResourcesNotFoundException(ADDRESS_NOT_FOUND, "code", MODULE);
        }
    }

    @Transactional
    public ResponseEntity<SingleResultDto<AddressDto>> updateAddress(Long actorId, Long addressId, AddressDto newAddressDto) {
        if (!actorRepository.existsByIdAndDeletedAtIsNull(actorId)) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        Address address = addressRepository.findByActorIdAndIdAndDeletedAtIsNull(actorId, addressId);
        if (address != null) {
            //checkDates(newAddressDto.getStartDate(), newAddressDto.getEndDate(), address);
            Address newAddress = modelMapper.map(newAddressDto, Address.class);
            newAddress.setId(address.getId());
            newAddress.setActor(address.getActor());
            newAddress.setReference(address.getReference());
            PhysicalAddress physicalAddress = modelMapper.map(newAddressDto.getPhysicalAddress(), PhysicalAddress.class);
            newAddress.setPhysicalAdress(physicalAddress);
            if (newAddressDto.getSummary() == null) {
                address.setSummary(generateSummary(newAddressDto));
            } else{
                newAddress.setSummary(newAddressDto.getSummary());
            }
            newAddress.getPhysicalAdress().setId(address.getPhysicalAdress().getId());
            physicalAdressRepository.save(newAddress.getPhysicalAdress());
            addressRepository.save(newAddress);

            // Return the created address
            return ResponseEntity.ok(SingleResultDto.<AddressDto>builder()
                    .data(this.modelMapper.map(newAddress, AddressDto.class))
                    .build());
        } else {
            throw new ResourcesNotFoundException(ADDRESS_NOT_FOUND, "code", MODULE);
        }
    }

    private void checkDates(Date startDate, Date endDate, Address address) {
        if (startDate != null) {
            // We should start using LocalDate instead of Date
            // Get today's date
            LocalDate today = LocalDate.now();

            // Convert Date to LocalDate
            LocalDate localStartDate =
                    startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

            if (!localStartDate.isBefore(today)) {
                address.setStartDate(startDate);
            } else {
                throw new ConflictException("The start date must be today or in the future", "START_DATE_CONFLICT", "The start date must be today or in the future", MODULE);
            }
        }
        if (endDate != null) {
            if (startDate == null || endDate.after(startDate)) {
                address.setEndDate(endDate);
            } else {
                throw new ConflictException("The end date must be after the start date", "END_DATE_CONFLICT", "The end date must be after the start date", MODULE);
            }
        }
    }
    @Transactional
    public ResponseEntity<InformativeMessage> deleteAddressById(Long actorId, Long addressId) {
        if (!actorRepository.existsByIdAndDeletedAtIsNull(actorId)) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        if (isLastAddress(actorId)) {
            throw new ConflictException("The actor must have at least one address", "LAST_ADDRESS_CONFLICT", "The actor must have at least one address", MODULE);
        }
        if (isAddressAssignedToOffer(addressId)) {
            throw new ConflictException("The address is assigned to a offer", "ADDRESS_ASSIGNED_TO_OFFER", "The address is assigned to a offer", MODULE);
        }
        Address address = addressRepository.findByActorIdAndIdAndDeletedAtIsNull(actorId, addressId);
        if (address != null) {
            address.onDelete();
            addressRepository.save(address);

            // Return the created address
            return ResponseEntity.ok(new InformativeMessage("Resource with ID " + addressId + " has been deleted successfully"));
        } else {
            throw new ResourcesNotFoundException(ADDRESS_NOT_FOUND, "code", MODULE);
        }
    }
    private boolean isAddressAssignedToOffer(Long addressId) {
        try {
            ResponseEntity<SimulationActorAddressResponse> simulationActorAddressResponse = restTemplate.getForEntity(
                    offerServiceUrl + "simulation_actor_address/" + addressId,
                    SimulationActorAddressResponse.class
            );
            // If status code is 200 OK, process the response
            if (simulationActorAddressResponse.getStatusCode() == HttpStatus.OK) {
                return !Objects.requireNonNull(simulationActorAddressResponse.getBody()).getPage().isEmpty();
            }
        } catch (Exception ex){
            return false;
        }
        return false;
    }
    private boolean isLastAddress(Long actorId) {
        return addressRepository.countByActorIdAndDeletedAtIsNull(actorId) == 1;
    }

    private String generateSummary(AddressDto addressDto) {
        return String.format("%s %s %s %s %s %s %s %s %s %s %s",
                addressDto.getNbr(),
                addressDto.getRoadType(),
                addressDto.getDistribution(),
                addressDto.getRoadExtension(),
                addressDto.getCity(),
                addressDto.getCommune(),
                addressDto.getZipCode(),
                addressDto.getCountry(),
                addressDto.getEntranceBuilding(),
                addressDto.getType(),
                addressDto.getSummary());
    }
}