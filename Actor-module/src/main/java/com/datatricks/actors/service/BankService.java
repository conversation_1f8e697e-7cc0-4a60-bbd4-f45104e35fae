package com.datatricks.actors.service;

import com.datatricks.common.exception.ConflictException;
import com.datatricks.common.exception.ResourcesNotFoundException;
import com.datatricks.common.exception.TechnicalException;
import com.datatricks.common.exception.handler.InformativeMessage;
import com.datatricks.common.model.Actor;
import com.datatricks.common.model.BankAccount;
import com.datatricks.common.model.BankBranch;
import com.datatricks.common.model.dto.*;
import com.datatricks.common.repository.ActorRepository;
import com.datatricks.common.repository.BankAccountRepository;
import com.datatricks.common.repository.BankBranchRepository;
import com.datatricks.common.repository.CountriesRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.transaction.Transactional;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.iban4j.Iban;
import org.iban4j.IbanUtil;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.Date;

import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;

@Slf4j
@Service
public class BankService {
    private static final String MODULE_NAME = "BANK_ACCOUNT";
    @Value("${api.iban.privateKey}")
    private String privateKey;
    @Value("${spring.services.static-tables.url}")
    private String staticTablesUrl;
    @Value("${api.iban.isMock}")
    private boolean isMock;
    private final RestTemplate restTemplate;
    private final BankAccountRepository bankAccountRepository;
    private final ActorRepository actorRepository;
    private final ModelMapper modelMapper;
    private final CountriesRepository countriesRepository;
    private static final String BANK_ACCOUNT_NOT_FOUND = "Bank account not found";
    private final BankBranchRepository bankBranchRepository;

    @Autowired
    public BankService(
            RestTemplate restTemplate,
            BankAccountRepository bankAccountRepository,
            ActorRepository actorRepository,
            ModelMapper modelMapper,
            CountriesRepository countriesRepository, BankBranchRepository bankBranchRepository) {
        this.restTemplate = restTemplate;
        this.bankAccountRepository = bankAccountRepository;
        this.actorRepository = actorRepository;
        this.modelMapper = modelMapper;
        this.countriesRepository = countriesRepository;
        this.bankBranchRepository = bankBranchRepository;
    }

    @Transactional
    public ResponseEntity<SingleResultDto<BankAccountDto>> getActorBankAccountById(Long actorId, Long bankAccountId) {
        if (!actorRepository.existsByIdAndDeletedAtIsNull(actorId)) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        BankAccount bankAccount = bankAccountRepository.findByActorIdIdAndIdAndDeletedAtIsNull(actorId, bankAccountId);

        if (bankAccount != null) {
            BankAccountDto bankAccountDto = this.modelMapper.map(bankAccount, BankAccountDto.class);
            countriesRepository.findByCode(bankAccount.getCountry()).ifPresent(country
                    -> bankAccountDto.setCountry(new CountryDto(country.getCode(), country.getLabel())));
            return ResponseEntity.ok(
                    SingleResultDto.<BankAccountDto>builder().data(bankAccountDto).build());
        } else {
            throw new ResourcesNotFoundException(BANK_ACCOUNT_NOT_FOUND, MODULE_NAME, MODULE_NAME);
        }
    }

    @Transactional
    public ResponseEntity<SingleResultDto<BankAccountDto>> createBankAccount(
            @PathVariable String actorReference, @RequestBody @Valid NewBankAccountDto newBankAccount) throws Exception {
        try {
            Actor actorOptional = actorRepository.findByReferenceAndDeletedAtIsNull(actorReference)
                    .orElseThrow(() -> new ResourcesNotFoundException("Actor not found", "ACTOR", MODULE_NAME));

            var bankAccount = modelMapper.map(newBankAccount, BankAccount.class);

            bankAccount.setCreatedAt(new Date());

            if (TRUE.equals(bankAccount.getIsPrincipal())) {
                deleteOldPrincipalBankAccount(actorReference);
            }
            bankAccount.setId(null);
            // Set the actor for the bank account
            bankAccount.setActorId(actorOptional);

            // Check if the IBAN is valid
            if (!IbanUtil.isValid(bankAccount.getIban())) {
                throw new ConflictException("Invalid IBAN", "INVALID_IBAN", "The IBAN is not valid", MODULE_NAME);
            }

            // Ensure start and end dates are not null and that the start date is before the end date
            if (bankAccount.getStartDate() != null
                    && bankAccount.getEndDate() != null
                    && (bankAccount.getStartDate().after(bankAccount.getEndDate()))) {
                throw new ConflictException("The start date is after the end date", "INVALID_DATES", "The start date is after the end date", MODULE_NAME);
            }
            // Ensure the validity is not null and that the validity is after the start date
            if (bankAccount.getValidity() != null
                    && bankAccount.getStartDate() != null
                    && bankAccount.getValidity().before(bankAccount.getStartDate())) {
                throw new ConflictException("The validity date is before the start date", "INVALID_DATES", "The validity date is before the start date", MODULE_NAME);
            }

            // Save the bank account
            BankAccount createdBankAccount = bankAccountRepository.save(bankAccount);

            // Return the created bank account
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(SingleResultDto.<BankAccountDto>builder()
                            .data(modelMapper.map(createdBankAccount, BankAccountDto.class))
                            .build());
        } catch (Exception e) {
            throw new Exception(e);

        }
    }

    @Transactional
    public ResponseEntity<SingleResultDto<BankAccountDto>> updateBankAccount(
            String actorReference, Long bankAccountId, NewBankAccountDto newBankAccount) {
        if (!actorRepository.existsByReferenceAndDeletedAtIsNull(actorReference)) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        BankAccount oldBankAccount = bankAccountRepository.findByActorIdReferenceAndIdAndDeletedAtIsNull(actorReference, bankAccountId)
                .orElseThrow(() -> new ResourcesNotFoundException(BANK_ACCOUNT_NOT_FOUND, MODULE_NAME, MODULE_NAME));

        if (oldBankAccount != null) {
            if (TRUE.equals(oldBankAccount.getIsPrincipal())) {
                deleteOldPrincipalBankAccount(actorReference);
            }
            var updatedBankAccount = modelMapper.map(newBankAccount, BankAccount.class);
            updatedBankAccount.setId(oldBankAccount.getId());
            updatedBankAccount.setCreatedAt(oldBankAccount.getCreatedAt());
            updatedBankAccount.setActorId(oldBankAccount.getActorId());
            updatedBankAccount.setModifiedAt(new Date());
            bankAccountRepository.save(updatedBankAccount);

            // Return the created bank account
            return ResponseEntity.ok(SingleResultDto.<BankAccountDto>builder()
                    .data(this.modelMapper.map(newBankAccount, BankAccountDto.class))
                    .build());
        } else {
            throw new ResourcesNotFoundException(BANK_ACCOUNT_NOT_FOUND, MODULE_NAME, MODULE_NAME);
        }
    }

    @Transactional
    protected void deleteOldPrincipalBankAccount(String actorReference) {
        BankAccount bankAccount = bankAccountRepository.findByActorIdReferenceAndIsPrincipalIsTrueAndDeletedAtIsNull(actorReference);
        if (bankAccount == null) {
            return;
        }
        bankAccount.setIsPrincipal(FALSE);
        bankAccount.setModifiedAt(new Date());
        bankAccountRepository.save(bankAccount);
    }

    @Transactional
    public ResponseEntity<SingleResultDto<BankAccountDto>> substituteBankAccount(
            Long actorId, Long bankAccountId, NewBankAccountDto newBankAccount) {
        if (!actorRepository.existsByIdAndDeletedAtIsNull(actorId)) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        BankAccount bankAccount = bankAccountRepository.findByActorIdIdAndIdAndDeletedAtIsNull(actorId, bankAccountId);
        if (bankAccount == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        if (bankAccount.getEndDate() != null && bankAccount.getEndDate().before(new Date())) {
            throw new ConflictException("Cannot substitute bank account", "CANNOT_SUBSTITUTE_BANK_ACCOUNT", "Cannot substitute an expired bank account", MODULE_NAME);
        }

        if (newBankAccount.getEndDate() != null) {
            bankAccount.setEndDate(newBankAccount.getEndDate());
        }
        if (newBankAccount.getStartDate() != null) {
            bankAccount.setStartDate(newBankAccount.getStartDate());
        }
        if (newBankAccount.getTitle() != null && !newBankAccount.getTitle().isEmpty()) {
            bankAccount.setTitle(newBankAccount.getTitle());
        }
        if (newBankAccount.getValidity() != null) {
            bankAccount.setValidity(newBankAccount.getValidity());
        }
        if (newBankAccount.getType() != null) {
            bankAccount.setType(newBankAccount.getType());
        }
        if (TRUE.equals(newBankAccount.getIsPrincipal())) {
            deleteOldPrincipalBankAccount(bankAccount.getActorId().getReference());
            bankAccount.setIsPrincipal(true);
        }
        bankAccount.setModifiedAt(new Date());
        bankAccountRepository.save(bankAccount);

        // Return the created bank account
        return ResponseEntity.ok(SingleResultDto.<BankAccountDto>builder()
                .data(this.modelMapper.map(bankAccount, BankAccountDto.class))
                .build());
    }

    public ResponseEntity<SingleResultDto<BankBranchDto>> validateIban(String iban) throws JsonProcessingException, TechnicalException {
        if (!IbanUtil.isValid(iban)) {
            throw new TechnicalException("Invalid IBAN", "", "");
        }
        if (!isMock) {
            String apiUrl = "https://api.ibanapi.com/v1/validate/" + iban + "?api_key={apiKey}";
            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

            // Create an HttpEntity with headers
            HttpEntity<String> entity = new HttpEntity<>(headers);
            // Send the request
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    apiUrl,
                    HttpMethod.GET,
                    entity,
                    String.class,
                    privateKey);

            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            return ResponseEntity.ok().body(SingleResultDto.<BankBranchDto>builder()
                    .data(objectMapper.readValue(responseEntity.getBody(), BankBranchDto.class))
                    .build());
        } else {
            var defaultBankBranch = IbanValidateResult.builder()
                    .result(200)
                    .data(Data.builder()
                            .countryCode("FR")
                            .countryName("FRANCE")
                            .bank(Bank.builder()
                                    .zip("928000")
                                    .bic("SOGEFRPP")
                                    .city("PUTEAUX")
                                    .address("17 TERRASSE VALMY")
                                    .bankName("SOCIETE GENERALE")
                                    .build())
                            .build()).build();
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            return ResponseEntity.ok().body(SingleResultDto.<BankBranchDto>builder()
                    .data(defaultBankBranch.toBranchDto())
                    .build());
        }
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteBankAccountById(Long actorId, Long bankAccountId) {
        if (!actorRepository.existsByIdAndDeletedAtIsNull(actorId)) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        BankAccount bankAccount = bankAccountRepository.findByActorIdIdAndIdAndDeletedAtIsNull(actorId, bankAccountId);

        if (bankAccount != null && FALSE.equals(bankAccount.getIsPrincipal())) {
            bankAccount.setDeletedAt(new Date());
            bankAccountRepository.save(bankAccount);

            // Return the created bank account
            return ResponseEntity.ok(new InformativeMessage("Resource with ID " + bankAccountId + " has been deleted successfully"));
        } else if (bankAccount != null && TRUE.equals(bankAccount.getIsPrincipal())) {
            throw new ConflictException("Cannot delete bank account", "CANNOT_DELETE_PRINCIPAL_BANK_ACCOUNT", "Cannot delete a principal bank account", MODULE_NAME);
        } else {
            throw new ResourcesNotFoundException(BANK_ACCOUNT_NOT_FOUND, MODULE_NAME, MODULE_NAME);
        }
    }

    @Transactional
    public ResponseEntity<SingleResultDto<BankAccountDto>> getPrincipalBankAccount(String actorReference) throws ResourcesNotFoundException {
        BankAccount bankAccount = bankAccountRepository.findByActorIdReferenceAndIsPrincipalIsTrueAndDeletedAtIsNull(actorReference);
        if (bankAccount == null) {
            throw new ResourcesNotFoundException("Principal bank account not found", "PRINCIPAL_BANK_ACCOUNT", MODULE_NAME);
        }

        BankAccountDto bankAccountDto = this.modelMapper.map(bankAccount, BankAccountDto.class);

        return ResponseEntity.ok(SingleResultDto.<BankAccountDto>builder().data(bankAccountDto).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<BankBranchDto>> getBankBranchByInterbankCode(String iban) throws JsonProcessingException {
        // Validate IBAN first
        if (!IbanUtil.isValid(iban)) {
            throw new ResourcesNotFoundException("Invalid IBAN", "IBAN_VALIDATION", "bank_account");
        }

        try {
            Iban _iban = Iban.valueOf(iban);

            // Check required IBAN components
            if (_iban.getCountryCode() == null || _iban.getBankCode() == null || _iban.getBranchCode() == null) {
                throw new ResourcesNotFoundException("IBAN doesn't have required components", "IBAN_STRUCTURE", "bank_account");
            }

            String interbankCode = _iban.getCountryCode().getAlpha2() + _iban.getBankCode() + _iban.getBranchCode();

            // Check local database first
            BankBranch bankBranch = bankBranchRepository.findByInterbankCodeAndDeletedAtIsNull(interbankCode);
            if (bankBranch != null) {
                return ResponseEntity.ok(SingleResultDto.<BankBranchDto>builder()
                        .data(modelMapper.map(bankBranch, BankBranchDto.class))
                        .build());
            }

            // Not found locally, fetch from API
            IbanApiResponse branchDto = fetchBankBranchFromApi(iban);

            // Check API response
            if (branchDto.getData() == null || branchDto.getData().getBank() == null) {
                throw new ResourcesNotFoundException("Cannot find bank branch information", "BANK_BRANCH", "bank_branch");
            }

            // Create bank branch DTO
            BankBranchStreamDto bankBranchStreamDto = createBankBranchStreamDto(_iban, branchDto);

            // Save to static tables service
            boolean saved = saveBankBranchToStaticTables(bankBranchStreamDto);

            // Create response
            BankBranchDto bankBranchResponse = BankBranchDto.builder()
                    .interbankCode(interbankCode)
                    .swift(branchDto.getData().getBank().getBic())
                    .domiciliation(branchDto.getData().getBank().getBankName())
                    .address(branchDto.getData().getBank().getAddress())
                    .city(branchDto.getData().getBank().getCity())
                    .postalCode(branchDto.getData().getBank().getZip())
                    .country(branchDto.getData().getCountryName())
                    .build();

            return ResponseEntity.ok(SingleResultDto.<BankBranchDto>builder()
                    .data(bankBranchResponse)
                    .build());

        } catch (Exception e) {
            log.error("Error fetching bank branch information for IBAN {}: {}", iban, e.getMessage());
            log.warn("Using mock data for bank branch information due to error above.");
            var defaultBankBranch = IbanValidateResult.builder()
                    .result(200)
                    .data(Data.builder()
                            .countryCode("FR")
                            .countryName("FRANCE")
                            .bank(Bank.builder()
                                    .zip("928000")
                                    .bic("SOGEFRPP")
                                    .city("PUTEAUX")
                                    .address("17 TERRASSE VALMY")
                                    .bankName("SOCIETE GENERALE")
                                    .build())
                            .build()).build();
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            return ResponseEntity.ok().body(SingleResultDto.<BankBranchDto>builder()
                    .data(defaultBankBranch.toBranchDto())
                    .build());
        }
    }

    private IbanApiResponse fetchBankBranchFromApi(String iban) throws JsonProcessingException {
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<String> entity = new HttpEntity<>(headers);

        String apiUrl = "https://api.ibanapi.com/v1/validate/" + iban + "?api_key={apiKey}";

        ResponseEntity<String> responseEntity = restTemplate.exchange(
                apiUrl,
                HttpMethod.GET,
                entity,
                String.class,
                privateKey);

        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper.readValue(responseEntity.getBody(), IbanApiResponse.class);
    }

    private BankBranchStreamDto createBankBranchStreamDto(Iban iban, IbanApiResponse response) {
        return BankBranchStreamDto.builder()
                .bank_id(iban.getBankCode())
                .country_code(iban.getCountryCode().getAlpha2())
                .bank_name(response.getData().getBank().getBankName())
                .code_swift(response.getData().getBank().getBic())
                .code_branch(iban.getBranchCode())
                .city(response.getData().getBank().getCity())
                .postal_code(response.getData().getBank().getZip())
                .address(response.getData().getBank().getAddress())
                .build();
    }

    private boolean saveBankBranchToStaticTables(BankBranchStreamDto dto) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

            HttpEntity<BankBranchStreamDto> entity = new HttpEntity<>(dto, headers);
            String staticTableUrl = staticTablesUrl + "banks";

            ResponseEntity<BankBranchStreamDto> response = restTemplate.postForEntity(
                    staticTableUrl, entity, BankBranchStreamDto.class);

            return response.getStatusCode() == HttpStatus.CREATED;
        } catch (Exception e) {
            throw new TechnicalException("Failed to save bank branch information: " + e.getMessage(), "BANK_BRANCH", "bank_branch");
        }
    }
}
