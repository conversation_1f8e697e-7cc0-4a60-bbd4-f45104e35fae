package com.datatricks.actors.service;

import com.datatricks.common.exception.ConflictException;
import com.datatricks.common.exception.ResourcesNotFoundException;
import com.datatricks.common.exception.handler.InformativeMessage;
import com.datatricks.common.model.*;
import com.datatricks.common.model.dto.*;
import com.datatricks.common.repository.*;
import com.datatricks.common.utils.JpaQueryFilters;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.datatricks.actors.utils.ActorsUtils.createReference;

@Service
public class ActorService {

    private final PhaseRepository phaseRepository;
    private final MilestoneRepository milestoneRepository;
    private static final String MODULE = "ACTOR";
    private static final String ACTOR_PREFIX = "ACT_";
    private final ActorRepository actorRepository;
    private final ActorPhaseRepository actorPhaseRepository;
    private final ActorRoleRepository actorRoleRepository;
    private final BusinessRepository businessSummaryRepository;
    private final PartiesRepository partiesSummaryRepository;
    private final ModelMapper modelMapper;
    private final RoleRepository roleRepository;
    private final ActivityRepository activityRepository;
    private final LegalCategoryRepository legalCategoryRepository;
    private final CountryRepository countryRepository;
    private final LegalActivitiesRepository legalActivitiesRepository;
    private static final String MILESTONE = "MILESTONE";
    private static final String NO_PHASE_FOUND_FOR_CODE = "No phase found for code ";
    private static final String PHASE = "PHASE";
    private static final String ACTEUR = "ACTEUR";
    private static final String ROLE_ASSOCIATED_TO_COMPANY = "COMPANY";
    private static final String ROLE_ASSOCIATED_TO_ACTEUR = "ACTEUR";
    private static final String CONFLICT_ROLE_IS_NOT_ASSOCIATED_TO_COMPANY = " Role is not associated to a company";
    private static final String CONFLICT_ROLE_IS_NOT_ASSOCIATED_TO_ACTOR = " Role is not associated to an actor";
    private final CommunicationMeanRepository communicationMeanRepository;

    public ActorService(
            PhaseRepository phaseRepository,
            MilestoneRepository milestoneRepository,
            ActorRepository actorRepository,
            ActorPhaseRepository actorPhaseRepository,
            ActorRoleRepository actorRoleRepository,
            BusinessRepository businessSummaryRepository,
            PartiesRepository partiesSummaryRepository,
            ModelMapper modelMapper, RoleRepository roleRepository,
            LegalActivitiesRepository legalActivitiesRepository,
            ActivityRepository activityRepository, LegalCategoryRepository legalCategoryRepository, CountryRepository countryRepository, CommunicationMeanRepository communicationMeanRepository) {
        this.phaseRepository = phaseRepository;
        this.milestoneRepository = milestoneRepository;
        this.actorRepository = actorRepository;
        this.actorPhaseRepository = actorPhaseRepository;
        this.actorRoleRepository = actorRoleRepository;
        this.businessSummaryRepository = businessSummaryRepository;
        this.partiesSummaryRepository = partiesSummaryRepository;
        this.modelMapper = modelMapper;
        this.roleRepository = roleRepository;
        this.activityRepository = activityRepository;
        this.legalCategoryRepository = legalCategoryRepository;
        this.countryRepository = countryRepository;
        this.legalActivitiesRepository = legalActivitiesRepository;
        this.communicationMeanRepository = communicationMeanRepository;
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ActorDto>> getActorById(Long id) {
        Actor actor = actorRepository.findByIdAndDeletedAtIsNull(id).orElse(null);
        if (actor != null) {
            ActorDto actorDto = this.modelMapper.map(actor, ActorDto.class);
            ActorRole actorRole = actorRoleRepository.findByActorIdIdAndIsPrincipalIsTrueAndDeletedAtIsNull(id);
            if (actorRole != null) {
                actorDto.setRole(this.modelMapper.map(actorRole.getRole(), RoleDto.class));
            }
            return ResponseEntity.ok(SingleResultDto.<ActorDto>builder().data(actorDto).build());
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ActorDto>> createActor(NewActorDto newActorDto) {
        Actor actor = new Actor(newActorDto);
        actor.setName(newActorDto.getName());
        actor.setCreatedAt(new Date());
        actor.setModifiedAt(new Date());
        actor.setReference(ACTOR_PREFIX + createReference());

        actor.setCountry(countryRepository.findByCode(newActorDto.getCountryCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Country not found", "COUNTRY", MODULE)));
        actor.setMilestone(milestoneRepository.findByCode("OUVERT")
                .orElseThrow(() -> new ResourcesNotFoundException("Milestone not found", MILESTONE, MODULE)));
        actor.setPhase(phaseRepository.findByCodeAndAssociatedTo(newActorDto.getPhaseCode(), ACTEUR).orElseThrow(
                () -> new ResourcesNotFoundException(NO_PHASE_FOUND_FOR_CODE + newActorDto.getPhaseCode(), PHASE, MODULE)));
        actor.setActivity(legalActivitiesRepository.findByCode(newActorDto.getActivityCode())
                .orElseThrow(() -> new ResourcesNotFoundException("LegalActivities not found", "ACTIVITY", MODULE)));
        actor.setLegalCategory(legalCategoryRepository.findByCode(newActorDto.getLegalCategoryCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Legal category not found", "LEGAL_CATEGORY", MODULE)));

        // Set the principal role
        Role role = roleRepository.findByCode(newActorDto.getRoleCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Role not found", "ROLE", MODULE));
        actor.setActorRole(saveNewActorRole(actor, role));

        actor.setAddresses(new HashSet<>());
        actor.setBankAccounts(new HashSet<>());
        actor.setSimulationActor(new HashSet<>());
        Actor createdActor = actorRepository.save(actor);
        // Update the Actors phase history
        saveNewActorPhase(actor);

        // Get the principal saved role
        ActorDto createdActorDto = modelMapper.map(createdActor, ActorDto.class);
        createdActorDto.setRole(this.modelMapper.map(role, RoleDto.class));


        return ResponseEntity.status(HttpStatus.CREATED)
                .body(SingleResultDto.<ActorDto>builder().data(createdActorDto).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ActorDto>> updateActor(Long id, NewActorDto newActorDto) {
        Optional<Actor> actor = actorRepository.findByIdAndDeletedAtIsNull(id);
        if (actor.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }

        actor.get().setExternalReference(newActorDto.getExternalReference());
        actor.get().setRegistrationCountry(newActorDto.getRegistrationCountry());
        actor.get().setShortName(newActorDto.getShortName());

        if (!Objects.equals(actor.get().getActivity().getCode(), newActorDto.getActivityCode())) {
            LegalActivities activity = legalActivitiesRepository.findByCode(newActorDto.getActivityCode()).orElseThrow(() -> new ResourcesNotFoundException("Activity not found", "ACTIVITY", MODULE));
            actor.get().setActivity(activity);
        }

        actor.get().setVat(newActorDto.getVat());
        actor.get().setCompanyCreationDate(newActorDto.getCompanyCreationDate());
        actor.get().setRegistrationDate(newActorDto.getRegistrationDate());
        actor.get().setType(newActorDto.getType());

        if (!Objects.equals(actor.get().getLegalCategory().getCode(), newActorDto.getLegalCategoryCode())) {
            LegalCategory legalCategory = legalCategoryRepository.findByCode(newActorDto.getLegalCategoryCode()).orElseThrow(() -> new ResourcesNotFoundException("Legal category not found", "LEGAL_CATEGORY", MODULE));
            actor.get().setLegalCategory(legalCategory);
        }

        actor.get().setNationalIdentity(newActorDto.getNationalIdentity());
        actor.get().setFeedChannel(newActorDto.getFeedChannel());
        actor.get().setMemo(newActorDto.getMemo());
        actor.get().setTaxReference(newActorDto.getTaxReference());
        actor.get().setCountry(countryRepository.findByCode(newActorDto.getCountryCode()).orElseThrow(() -> new ResourcesNotFoundException("Country not found", "COUNTRY", MODULE)));

        if (!Objects.equals(actor.get().getPhase().getCode(), newActorDto.getPhaseCode())) {
            Phase phase = phaseRepository.findByCodeAndAssociatedTo(newActorDto.getPhaseCode(), "ACTEUR").stream().findFirst().orElse(null);
            if (phase != null) {
                actor.get().setPhase(phase);
            } else {
                // Handle the case where no phase is found
                throw new ResourcesNotFoundException("No phase found for code " + newActorDto.getPhaseCode(), "PHASE", MODULE);
            }
        }
        actor.get().setModifiedAt(new Date());
        actorRepository.save(actor.get());

        ActorDto updatedActorDto = modelMapper.map(actor.get(), ActorDto.class);
        ActorRole actorRole = actorRoleRepository.findByActorIdIdAndIsPrincipalIsTrueAndDeletedAtIsNull(id);
        updatedActorDto.setRole(this.modelMapper.map(actorRole.getRole(), RoleDto.class));

        return ResponseEntity.ok(SingleResultDto.<ActorDto>builder().data(updatedActorDto).build());
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteActorById(Long id) {
        Optional<Actor> actor = actorRepository.findByIdAndDeletedAtIsNull(id);
        if (actor.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
        Actor actorToDelete = actor.get();
        actorToDelete.setDeletedAt(new Date());
        actorRepository.save(actorToDelete);

        return ResponseEntity.ok(new InformativeMessage("Resource with ID " + id + " has been deleted successfully"));
    }

    private void saveNewActorPhase(Actor actor) {
        ActorPhase actorPhase = new ActorPhase();
        if (actor.getPhase() != null && actor.getPhase().getCode() != null) {
            actorPhase.setPhase(actor.getPhase().getCode());
        }

        if (actor.getMilestone() != null && actor.getMilestone().getCode() != null) {
            actorPhase.setMilestone(actor.getMilestone().getCode());
        }
        actorPhase.setCreatedAt(new Date());
        actorPhase.setActorId(actor.getId());
        actorPhaseRepository.save(actorPhase);
    }

    @Transactional
    public ResponseEntity<PageDto<ActorDto>> getActors(Map<String, String> params) {
        JpaQueryFilters<Actor> filters = new JpaQueryFilters<>(params, Actor.class);
        Page<Actor> page = actorRepository.findAll(filters.getSpecification(), filters.getPageable());
        List<ActorDto> filteredActors = page.stream()
                .map(actor -> this.modelMapper.map(actor, ActorDto.class))
                .toList();
        for (ActorDto pageableDto : filteredActors) {
            ActorRole actorRole = actorRoleRepository.findByActorIdIdAndIsPrincipalIsTrueAndDeletedAtIsNull(
                    pageableDto.getId());
            if (actorRole != null) {
                pageableDto.setRole(this.modelMapper.map(actorRole.getRole(), RoleDto.class));
            }
        }
        return ResponseEntity.ok(PageDto.<ActorDto>builder()
                .data(filteredActors)
                .total(page.getTotalElements())
                .build());
    }

    @Transactional
    public ResponseEntity<PageDto<BusinessSummaryDto>> getBusinesses(Map<String, String> params) {
        JpaQueryFilters<BusinessSummary> filters = new JpaQueryFilters<>(params, BusinessSummary.class);
        Page<BusinessSummary> page = businessSummaryRepository.findAll(filters.getSpecification(), filters.getPageable());
        List<BusinessSummaryDto> filteredBusiness = page.stream()
                .map(business -> this.modelMapper.map(business, BusinessSummaryDto.class))
                .toList();

        return ResponseEntity.ok(PageDto.<BusinessSummaryDto>builder()
                .data(filteredBusiness)
                .total(page.getTotalElements())
                .build());
    }

    @Transactional
    public ResponseEntity<PageDto<PartiesSummaryDto>> getParties(Map<String, String> params) {
        JpaQueryFilters<PartySummary> filters = new JpaQueryFilters<>(params, PartySummary.class);
        Page<PartySummary> page = partiesSummaryRepository.findAll(filters.getSpecification(), filters.getPageable());
        List<PartiesSummaryDto> filteredBusiness = page.stream()
                .map(business -> this.modelMapper.map(business, PartiesSummaryDto.class))
                .toList();

        return ResponseEntity.ok(PageDto.<PartiesSummaryDto>builder()
                .data(filteredBusiness)
                .total(page.getTotalElements())
                .build());
    }

    @Transactional
    protected Set<ActorRole> saveNewActorRole(Actor actor, Role role) {
        if (!Objects.equals(actor.getActorRole(), null)) {
            actor.getActorRole().clear();
        } else {
            actor.setActorRole(new HashSet<>());
        }
        if (actor.getType() == ActorTypes.MANAGEMENT_COMPANY) {
            if (role.getStaticRoleCode().getAssociatedTo().contains(ROLE_ASSOCIATED_TO_COMPANY)) {
                actor.getActorRole().add(new ActorRole(actor, role, true));
            } else {
                throw new ConflictException(
                        CONFLICT_ROLE_IS_NOT_ASSOCIATED_TO_COMPANY,
                        "CONFLICT_ROLE_IS_NOT_ASSOCIATED_TO_COMPANY",
                        CONFLICT_ROLE_IS_NOT_ASSOCIATED_TO_COMPANY,
                        MODULE);
            }
        } else {
            if (role.getStaticRoleCode().getAssociatedTo().contains(ROLE_ASSOCIATED_TO_ACTEUR)) {
                actor.getActorRole().add(new ActorRole(actor, role, true));
            } else {
                throw new ConflictException(
                        CONFLICT_ROLE_IS_NOT_ASSOCIATED_TO_ACTOR,
                        "CONFLICT_ROLE_IS_NOT_ASSOCIATED_TO_ACTOR",
                        CONFLICT_ROLE_IS_NOT_ASSOCIATED_TO_ACTOR,
                        MODULE);
            }
        }
        return actor.getActorRole();
    }

    @Transactional
    public ResponseEntity<PageDto<BillingParametersEmailDto>> getActorEmails(String actorReference) {
        Actor actor = actorRepository.findByReference(actorReference)
                .orElseThrow(() -> new ResourcesNotFoundException("Actor not found", "code", MODULE));

        List<BillingParametersEmailDto> emails = new ArrayList<>();
        if (actor.getContacts() != null) {
            for (Contact contact : actor.getContacts()) {
                if (contact.getCommunicationMeans() != null) {
                    List<CommunicationMean> communicationMeans = communicationMeanRepository
                            .findByContactIdIdAndDeletedAtIsNull(contact.getId());
                    communicationMeans.stream()
                            .filter(communicationMean ->
                                    communicationMean.getType() != null &&
                                            communicationMean.getType().equals(CommunicationMeanTypes.EMAIL) &&
                                            communicationMean.getDeletedAt() == null)
                            .forEach(communicationMean -> emails.add(BillingParametersEmailDto.builder()
                                    .id(communicationMean.getId())
                                    .firstName(contact.getFirstName())
                                    .lastName(contact.getLastName())
                                    .email(communicationMean.getReference())
                                    .title(contact.getTitle())
                                    .preferred(communicationMean.getPreferred())
                                    .build()));
                }
            }
        }

        return ResponseEntity.ok(PageDto.<BillingParametersEmailDto>builder()
                .data(emails)
                .total(emails.size())
                .build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ActorDto>> getActorByReference(String reference) {
        Actor actor = actorRepository.findByReferenceAndDeletedAtIsNull(reference).orElse(null);
        if (actor != null) {
            ActorDto actorDto = this.modelMapper.map(actor, ActorDto.class);
            ActorRole actorRole = actorRoleRepository.findByActorIdIdAndIsPrincipalIsTrueAndDeletedAtIsNull(actor.getId());
            if (actorRole != null) {
                actorDto.setRole(this.modelMapper.map(actorRole.getRole(), RoleDto.class));
            }
            return ResponseEntity.ok(SingleResultDto.<ActorDto>builder().data(actorDto).build());
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }
}
