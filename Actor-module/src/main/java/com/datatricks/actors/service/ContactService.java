package com.datatricks.actors.service;

import com.datatricks.common.exception.EntityNotFoundException;
import com.datatricks.common.exception.ResourcesNotFoundException;
import com.datatricks.common.model.Actor;
import com.datatricks.common.model.Contact;
import com.datatricks.common.model.Element;
import com.datatricks.common.model.Position;
import com.datatricks.common.model.dto.*;
import com.datatricks.common.repository.ActorRepository;
import com.datatricks.common.repository.ContactRepository;
import com.datatricks.common.repository.PositionRepository;
import com.datatricks.common.utils.JpaQueryFilters;
import jakarta.transaction.Transactional;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;

@Service
public class ContactService {

    private final ContactRepository contactRepository;
    private final ActorRepository actorRepository;
    private final ModelMapper modelMapper;
    private final PositionRepository positionRepository;
    private static final String MODULE = "CONTACT";
    private static final String OFFER_NOT_FOUND = "Contact not found";
    private static final String ACTOR_NOT_FOUND = "Actor not found";
    private static final String CONTACT = "CONTACT";
    private static final String ACTOR = "ACTOR";

    public ContactService(ContactRepository contactRepository,
                          ActorRepository actorRepository,
                          PositionRepository positionRepository,
                          ModelMapper modelMapper) {
        this.contactRepository = contactRepository;
        this.actorRepository = actorRepository;
        this.positionRepository = positionRepository;
        this.modelMapper = modelMapper;
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ContactDto>> getActorContactById(@PathVariable Long actorId, @PathVariable Long contactId) {
        if (!actorRepository.existsByIdAndDeletedAtIsNull(actorId)) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        Contact contact = contactRepository.findByActorIdIdAndIdAndDeletedAtIsNull(actorId, contactId).orElseThrow(
                () -> new EntityNotFoundException(OFFER_NOT_FOUND, MODULE));

        return ResponseEntity.ok()
                .body(SingleResultDto.<ContactDto>builder()
                        .data(modelMapper.map(contact, ContactDto.class)).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ContactDto>> createNewContact(Long actorId, NewContactDto contact) {
        Actor actor = actorRepository.findByIdAndDeletedAtIsNull(actorId).orElseThrow(
                () -> new ResourcesNotFoundException(OFFER_NOT_FOUND, CONTACT, MODULE));

        Position quality = positionRepository.findByCodeAndDeletedAtIsNull(contact.getQualityCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Quality not found", CONTACT, MODULE));

        if (TRUE.equals(contact.getPreferred())) {
            deleteOldPreferredContact(actorId);
        }

        Contact nawContact = modelMapper.map(contact, Contact.class);
        nawContact.setQuality(quality);

        Contact savedContact = saveNewContact(actor, nawContact);

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(SingleResultDto.<ContactDto>builder()
                        .data(modelMapper.map(savedContact, ContactDto.class)).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ContactDto>> updateExistingContact(Long actorId, Long contactId, NewContactDto newContact) throws EntityNotFoundException {
        actorRepository.findByIdAndDeletedAtIsNull(actorId).orElseThrow(
                () -> new ResourcesNotFoundException(ACTOR_NOT_FOUND, ACTOR, MODULE));

        Contact oldContact = contactRepository.findByActorIdIdAndIdAndDeletedAtIsNull(actorId, contactId).orElseThrow(
                () -> new ResourcesNotFoundException(OFFER_NOT_FOUND, ACTOR, MODULE));

        Position quality = positionRepository.findByCodeAndDeletedAtIsNull(newContact.getQualityCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Quality not found", ACTOR, MODULE));

        if (TRUE.equals(newContact.getPreferred())) {
            deleteOldPreferredContact(actorId);
        }

        Contact contactToUpdate = modelMapper.map(newContact, Contact.class);
        contactToUpdate.setQuality(quality);

        Contact updatedContact = saveExistingContact(oldContact, contactToUpdate);

        return ResponseEntity.status(HttpStatus.CREATED).body(SingleResultDto.<ContactDto>builder()
                .data(modelMapper.map(updatedContact, ContactDto.class)).build());
    }

    @Transactional
    protected void deleteOldPreferredContact(Long actorId) {
        Contact contact = contactRepository.findByActorIdIdAndPreferredIsTrueAndDeletedAtIsNull(actorId);
        if (contact != null) {
            contact.setPreferred(FALSE);
            contact.setModifiedAt(new Date());

            Contact updatedContact = contactRepository.save(contact);
        }
    }

    @Transactional
    protected Contact saveNewContact(Actor actor, Contact contact) {
        // Set the actor for the contact
        contact.setActorId(actor);
        // Save the contact
        contact.setCreatedAt(new Date());
        // Return the created contact
        return contactRepository.save(contact);
    }

    @Transactional
    protected Contact saveExistingContact(Contact oldContact, Contact newContact) {
        newContact.setId(oldContact.getId());
        newContact.setCreatedAt(oldContact.getCreatedAt());
        newContact.setActorId(oldContact.getActorId());
        newContact.setModifiedAt(new Date());

        return contactRepository.save(newContact);
    }

    @Transactional
    public ResponseEntity<PageDto<ContactDto>> getActorContacts(Long id, Map<String, String> params) {
        actorRepository.findByIdAndDeletedAtIsNull(id).orElseThrow(
                () -> new ResourcesNotFoundException(ACTOR_NOT_FOUND, ACTOR, MODULE));

        JpaQueryFilters<Contact> filters = new JpaQueryFilters<>(params, Contact.class);
        Specification<Contact> actorIdSpec = (root, query, criteriaBuilder) ->
                criteriaBuilder.equal(root.get("actorId").get("id"), id);
        Page<Contact> page =
                contactRepository.findAll(filters.getSpecification().and(actorIdSpec), filters.getPageable());
        List<ContactDto> contacts = page.stream()
                .map(contact -> this.modelMapper.map(contact, ContactDto.class))
                .toList();
        return new ResponseEntity<>(
                PageDto.<ContactDto>builder()
                        .data(contacts)
                        .total(page.getTotalElements())
                        .build(),
                HttpStatus.OK);
    }

    @Transactional
    public void deleteContactById(Long actorId, Long contactId) {
        actorRepository.findByIdAndDeletedAtIsNull(actorId).orElseThrow(
                () -> new ResourcesNotFoundException(ACTOR_NOT_FOUND, ACTOR, MODULE));

        Contact contact = contactRepository.findByActorIdIdAndIdAndDeletedAtIsNull(actorId, contactId).orElseThrow(
                () -> new ResourcesNotFoundException(OFFER_NOT_FOUND, ACTOR, MODULE));

        contact.setDeletedAt(new Date());
        Contact deletedContact = contactRepository.save(contact);
    }

    @Transactional
    public ResponseEntity<PageDto<PositionDto>> getAllPositions(Map<String, String> params) {

        JpaQueryFilters<Position> filters = new JpaQueryFilters<>(params, Position.class);

        Page<Position> page = positionRepository.findAll(filters.getSpecification(), filters.getPageable());

        List<PositionDto> positions = page.getContent().stream()
                .map(position -> modelMapper.map(position, PositionDto.class))
                .toList();

        PageDto<PositionDto> response = PageDto.<PositionDto>builder()
                .data(positions)
                .total(page.getTotalElements())
                .build();

        return ResponseEntity.ok(response);
    }

}
