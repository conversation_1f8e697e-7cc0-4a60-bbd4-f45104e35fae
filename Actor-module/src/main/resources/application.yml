spring:
  application:
    name: ActorService
  services:
    auth:
      url: http://auth:8810/api/v1/
    offer:
      url: http://offer:8803/api/v1/
    asset:
      url: http://asset:8805/api/v1/    
    engine:
      url: http://engine-fo:8806/api/v1/
    static-tables:
      url: http://static-tables:8807/api/v1/static-tables/
  datasource:
    driver-class-name: org.postgresql.Driver
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
  springdoc:
    api-docs:
      path: /v3/api-docs
    swagger-ui:
      path: /swagger-ui.html
server:
  port: 8802
  tomcat:
    additional-tld-skip-patterns: "*.tld"
api:
  response:
    activateDebugInfo: true
  iban:
    privateKey: 4a0beb7e76894ade314ad8ec8320daf889e30309
    isMock: true #true in case we dont want to send useless request to api
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false
# local profile configuration
---
spring:
  config:
    activate:
      on-profile: default
  services:
    auth:
      url: http://localhost:8710/api/v1/
    offer:
      url: http://localhost:8703/api/v1/
    asset:
      url: http://localhost:8705/api/v1/
    engine:
      url: http://localhost:8706/api/v1/
    static-tables:
      url: http://localhost:8707/api/v1/static-tables/
  datasource:
    url: *****************************************
    username: postgres
    password: changeme
  jpa:
    hibernate:
      naming:
        physical-strategy: com.datatricks.actors.configuration.CustomHibernateNamingStrategy
      ddl-auto: validate
    open-in-view: false
    show-sql: true
    properties:
      hibernate:
        format_sql: true
server:
  port: 8702
logging:
  level:
    root: info
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
# docker local profile configuration
---
spring:
  config:
    activate:
      on-profile: docker-local
  datasource:
    url: *******************************************
    username: postgres
    password: changeme
  jpa:
    hibernate:
      naming:
        physical-strategy: com.datatricks.actors.configuration.CustomHibernateNamingStrategy
      ddl-auto: validate
    open-in-view: false
    show-sql: true
    properties:
      hibernate:
        format_sql: true
# DEV profile configuration
---
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}?prepareThreshold=0
    username: ${DB_USER}
    password: ${DB_PASSWORD}
  jpa:
    hibernate:
      naming:
        physical-strategy: com.datatricks.actors.configuration.CustomHibernateNamingStrategy
      ddl-auto: validate
    open-in-view: false
    show-sql: true
    properties:
      hibernate:
        format_sql: true
logging:
  level:
    root: info
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
# TEST profile configuration
---
spring:
  config:
    activate:
      on-profile: test
  datasource:
    url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}?prepareThreshold=0
    username: ${DB_USER}
    password: ${DB_PASSWORD}
  jpa:
    hibernate:
      naming:
        physical-strategy: com.datatricks.actors.configuration.CustomHibernateNamingStrategy
      ddl-auto: validate
    open-in-view: false
    show-sql: false
    properties:
      hibernate:
        format_sql: true
logging:
  level:
    root: info
  pattern:
    console: "%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"
