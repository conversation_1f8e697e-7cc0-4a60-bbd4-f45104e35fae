package com.datatricks.common.model;

import com.datatricks.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.util.Date;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "addresses")
public class Address extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "reference", unique = true)
    @JsonProperty("reference")
    private String reference;

    @Column(name = "city")
    @JsonProperty("city")
    @NotBlank(message = "city:please provide a city")
    private String city;

    @Column(name = "country")
    @JsonProperty("country")
    private String country;

    @Column(name = "zip_code")
    @JsonProperty("zip_code")
    @NotBlank(message = "zip_code:please provide a zip_code")
    private String zipCode;

    @Column(name = "entrance_building")
    @JsonProperty("entrance_building")
    private String entranceBuilding;

    @Column(name = "type")
    @JsonProperty("type")
    private String type;

    @Column(name = "nbr")
    @JsonProperty("number")
    @NotNull(message = "number:please provide a number")
    private String nbr;

    @Column(name = "distribution")
    @JsonProperty("distribution")
    private String distribution;

    @Column(name = "road_extension")
    @JsonProperty("road_extension")
    private String roadExtension;

    @Column(name = "Commune")
    @JsonProperty("commune")
    private String commune;

    @Column(name = "road_type")
    @JsonProperty("road_type")
    private String roadType;

    @Column(name = "subsidiary")
    @JsonProperty("subsidiary")
    @NotNull(message = "subsidiary:please provide an subsidiary")
    private Boolean subsidiary;

    @Column(name = "headquarter")
    @JsonProperty("headquarter")
    @NotNull(message = "headquarter: please provide a headquarter")
    private Boolean headquarter;

    @Column(name = "is_billing")
    @JsonProperty("is_billing")
    @NotNull(message = "is_billing:please provide is_billing field")
    private Boolean isBilling;

    @Column(name = "is_delivery")
    @JsonProperty("is_delivery")
    @NotNull(message = "is_delivery:please provide is_delivery field")
    private Boolean isDelivery;

    @Column(name = "summary")
    @JsonProperty("summary")
    private String summary;

    @Column(name = "start_date")
    @JsonProperty("start_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date startDate;

    @Column(name = "end_date")
    @JsonProperty("end_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date endDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "actorId")
    private Actor actor;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "physicalAdressId")
    private PhysicalAddress physicalAdress;

    public Address(Long id) {
        this.id = id;
    }
}
