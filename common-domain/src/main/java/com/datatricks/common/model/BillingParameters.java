package com.datatricks.common.model;

import com.datatricks.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.Valid;
import lombok.*;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "billing_parameters", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"actor_reference", "actor_role_code", "deleted_at"})
})
public class BillingParameters extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @JsonProperty("invoice_delery_mode")
    @Enumerated(EnumType.STRING)
    @Column(name = "invoice_delivery_mode")
    @Valid
    private InvoiceDeliveryMethod invoiceDeliveryMode;

    @JsonProperty("notes")
    @Column(name = "notes")
    private String notes;

    @JsonProperty("actor_role_code")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "actor_role_code", referencedColumnName = "code")
    private Role role;

    @JsonProperty("copy_count")
    @Column(name = "copy_count")
    private Integer copyCount;

    @JsonProperty("invoice_edition_date_limit")
    @Column(name = "invoice_edition_date_limit")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate invoiceEditionDateLimit;

    @JsonProperty("communication_means")
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "billingParameters")
    @Builder.Default
    private List<CommunicationMean> communicationMeansList = new ArrayList<>();

    @JsonProperty("address")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "address_id")
    private Address address;

    @JsonProperty("actor_reference")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "actor_reference", referencedColumnName = "reference")
    private Actor actor;
}
