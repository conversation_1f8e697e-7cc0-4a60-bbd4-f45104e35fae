package com.datatricks.common.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class BankBranchDto implements PageableDto {

    @JsonProperty("id")
    private Long id;

    @JsonProperty("interbank_code")
    @Schema(description = "Interbank code", example = "30002")
    private String interbankCode;

    @JsonProperty("swift")
    @Schema(description = "Swift code", example = "BNPAFRPPXXX")
    private String swift;

    @JsonProperty("domiciliation")
    @Schema(description = "Domiciliation", example = "BNP PARIBAS")
    private String domiciliation;

    @JsonProperty("region")
    @Schema(description = "Region", example = "Ile-de-France")
    private String region;

    @JsonProperty("address")
    @Schema(description = "Address", example = "16 rue de la paix")
    private String address;

    @JsonProperty("city")
    @Schema(description = "City", example = "Paris")
    private String city;

    @JsonProperty("postal_code")
    @Schema(description = "Postal code", example = "75001")
    private String postalCode;

    @JsonProperty("country")
    @Schema(description = "Country", example = "France")
    private String country;
}
