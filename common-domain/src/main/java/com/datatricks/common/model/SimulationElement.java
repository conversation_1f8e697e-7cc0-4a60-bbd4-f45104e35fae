package com.datatricks.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.util.Date;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "SIMULATION_ELEMENT")
public class SimulationElement extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "reference", unique = true)
    @JsonProperty("reference")
    private String reference;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "element_id")
    private Element element;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "simulation_id")
    private Simulation simulation;

    @Column(name = "start_date")
    private Date startDate;

    @Column(name = "end_date")
    private Date endDate;

    @Column(name = "note")
    private String note;

    @Column(name = "usage")
    @JsonProperty("usage")
    @Enumerated(EnumType.STRING)
    private UsageTypes usage;

    @Column(name = "actor_type")
    @JsonProperty("actor_type")
    @Enumerated(EnumType.STRING)
    private ActorTypes actorType;

    @Column(name = "activity_code")
    private String activityCode;

    @Column(name = "feed_channel")
    private String feedChannel;

    @Column(name = "quantity")
    private Integer quantity;

    @Column(name = "discount")
    private Double discount;

    @Column(name = "discount_amount_ttc")
    private Double discountAmountTtc;

    @Column(name = "discount_amount_Ht")
    private Double discountAmountHt;



    public SimulationElement(Long id) {
        this.id = id;
    }

    @PostPersist
    private void generateReference() {
        if (reference == null) {
            long timestamp = System.currentTimeMillis();
            reference = "SE" + id + timestamp;
        }
    }
}

