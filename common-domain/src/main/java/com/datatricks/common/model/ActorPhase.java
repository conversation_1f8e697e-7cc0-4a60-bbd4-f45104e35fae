package com.datatricks.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.util.Date;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(
        name = "actor_phases_history",
        indexes = {@Index(name = "phaseHistory_actor_index", columnList = "actor_id")})
public class ActorPhase {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "phase")
    @JsonProperty("phase")
    private String phase;

    @Column(name = "milestone")
    @JsonProperty("milestone")
    private String milestone;

    @Column(name = "actor_id")
    private Long actorId;

    @Column(name = "createdAt")
    @JsonProperty("created_at")
    private Date createdAt;
}
