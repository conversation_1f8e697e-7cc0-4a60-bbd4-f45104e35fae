package com.datatricks.common.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PositionDto implements PageableDto {
    @Schema(description = "Country code", example = "FR", type = "string")
    private String code;
    @Schema(description = "Country label", example = "France", type = "string")
    private String label;
}
