package com.datatricks.common.model;

import com.datatricks.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.util.Date;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "fiscal_year")
public class FiscalYear {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "Exercice")
    @NotNull(message = "Exercice:please provide an exercice")
    private String exercice;
    @Column(name = "start_year")
    @NotNull(message = "startYear:please provide a start year")
    private Integer startYear;
    @Column(name = "start_month")
    @NotNull(message = "startMonth:please provide a start month")
    private Integer startMonth;
    @Column(name = "end_year")
    @NotNull(message = "endYear:please provide an end year")
    private Integer endYear;
    @Column(name = "end_month")
    @NotNull(message = "endMonth:please provide an end month")
    private Integer endMonth;
    @Column(name = "closing_date")
    @Temporal(TemporalType.DATE)
    private Date closingDate;
    @Column(name = "closing_type")
    @Enumerated(EnumType.STRING)
    private FiscalYearClosingTypes closingType;
    @Column(name = "number_of_months")
    private Integer numberOfMonths;
    @Column(name = "entry_date")
    @NotNull(message = "entryDate:please provide an entry date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date entryDate;
    @Column(name = "recovery")
    @NotNull(message = "recovery:please provide a recovery")
    private Integer recovery;

    @Column(name = "createdAt")
    @JsonProperty("created_at")
    private Date createdAt;

    @Column(name = "modifiedAt")
    @JsonProperty("modified_at")
    private Date modifiedAt;

    @Column(name = "deletedAt")
    @JsonProperty("deleted_at")
    private Date deletedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "currency_id")
    @NotNull(message = "currency:please provide a currency")
    private Currency currency;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "actor_id")
    @NotNull(message = "actor:please provide an actor")
    private Actor actor;
}
