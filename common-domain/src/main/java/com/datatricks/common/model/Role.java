package com.datatricks.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "roles")
public class Role extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "code", unique = true)
    private String code;

    @Column(name = "label")
    private String label;

    @Column(name = "active")
    private Boolean active;

    @ManyToOne
    @JoinColumn(name = "static_role_code", referencedColumnName = "code")
    private StaticRole staticRoleCode;

    public Role(Long id) {
        this.id = id;
    }

    public Role(String roleCode) {
        this.code = roleCode;
    }

    public Role(String code, String label, Boolean active) {
        this.code = code;
        this.label = label;
        this.active = active;
    }
}
