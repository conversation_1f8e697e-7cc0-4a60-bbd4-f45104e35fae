package com.datatricks.common.model;

import com.datatricks.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(
        name = "simulation_actors",
        uniqueConstraints = @UniqueConstraint(columnNames = {"actor_reference", "simulation_reference", "role_code", "deleted_at"}))
public class SimulationActor extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "reference", unique = true)
    @JsonProperty("reference")
    private String reference;

    @Column(name = "start_date")
    @JsonProperty("start_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate startDate;

    @Column(name = "end_date")
    @JsonProperty("end_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate endDate;

    @Column(name = "pid")
    @JsonProperty("pid")
    private String pid;

    @Column(name = "extrait_kbis")
    @JsonProperty("extrait_kbis")
    private String extraitKbis;

    @Column(name = "bilan")
    @JsonProperty("bilan")
    private String bilan;

    @Column(name = "cni")
    @JsonProperty("cni")
    private String cni;

    @Column(name = "rib")
    @JsonProperty("rib")
    private String rib;

    @Column(name = "mandat_sepa")
    @JsonProperty("mandat_sepa")
    private String mandatSepa;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "actor_reference", referencedColumnName = "reference")
    private Actor actor;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "simulation_reference")
    private Simulation simulation;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_code", referencedColumnName = "code")
    private Role role;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "delegationCode", referencedColumnName = "code")
    @JsonProperty("delegation")
    private Delegation delegation;

    @OneToMany(mappedBy = "simulationActorId", fetch = FetchType.LAZY)
    private List<Rental> rentals;

    @OneToMany(mappedBy = "simulationActorId", fetch = FetchType.LAZY)
    private List<Accessory> accessories;

    /*@OneToMany(mappedBy = "simulationActor", fetch = FetchType.LAZY)
    @JsonProperty("payments")
    private List<SimulationActorPayments> payments;*/

    public SimulationActor(Long id) {
        // For database insertions
        this.id = id;
    }

    @PostPersist
    private void generateReference() {
        if (reference == null) {
            long timestamp = System.currentTimeMillis();
            reference = "SA" + id + timestamp;
        }
    }
}
