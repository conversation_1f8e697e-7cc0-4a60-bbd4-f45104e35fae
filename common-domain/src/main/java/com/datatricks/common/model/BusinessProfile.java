package com.datatricks.common.model;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "business_profiles",
        uniqueConstraints = @UniqueConstraint(columnNames = {"national_Issuer_Number", "actor_id", "Issuer_Number_Internal_Transfer", "deleted_at"}))
public class BusinessProfile extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "code")
    private String code;

    @Column(name = "Calender")
    @Enumerated(EnumType.STRING)
    private CalendarTypes calender;

    @Column(name = "national_Issuer_Number")
    private String nationalIssuerNumber;

    @Column(name = "Issuer_Number_Internal_Transfer")
    private String issuerNumberInternalTransfer;

    @Column(name = "ICS")
    private String ics;

    @Column(name = "minimum_due_date")
    private Integer minimumDueDate;

    @Column(name = "Logo")
    private String logo;

    @Column(name = "FICP_grouping_code")
    private String ficpGroupingCode;

    @Column(name = "tax_system")
    private String taxSystem;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "actor_id")
    private Actor actor;
}
