package com.datatricks.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "parameters")
public class StaticParameter {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@JsonProperty("id")
	private Long id;

	@Column(name = "code", unique = true)
	private String code;

	@Column(name = "label")
	private String label;

	@Column(name = "associated_to")
	private String associatedTo;

    public StaticParameter(String nature) {
		this.code = nature;
    }
}
