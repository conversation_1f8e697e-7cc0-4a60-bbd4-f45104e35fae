package com.datatricks.common.model;

import com.datatricks.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Immutable;

import java.util.Date;

@Entity
@Table(name = "business_summary")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Immutable
public class BusinessSummary extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "reference", unique = true)
    @JsonProperty("reference")
    private String reference;

    @Column(name = "external_reference")
    @JsonProperty("external_reference")
    private String externalReference;

    @Column(name = "short_name")
    @JsonProperty("short_name")
    private String shortName;

    @Column(name = "name")
    @JsonProperty("name")
    private String name;

    @Column(name = "vat")
    @JsonProperty("vat")
    private String vat;

    @Column(name = "registration_date")
    @JsonProperty("registration_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date registrationDate;

    @Column(name = "company_creation_date")
    @JsonProperty("company_creation_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date companyCreationDate;

    @Column(name = "registration_country")
    @JsonProperty("registration_country")
    private String registrationCountry;

    @Column(name = "register_type")
    @JsonProperty("registerType")
    private String registerType;

    @Column(name = "register_number")
    @JsonProperty("register_number")
    private String registerNumber;

    @Column(name = "type")
    @JsonProperty("type")
    private String type;

    @Column(name = "national_identity", unique = true)
    @JsonProperty("national_identity")
    private String nationalIdentity;

    @Column(name = "feed_channel")
    @JsonProperty("feed_channel")
    private String feedChannel;

    @Column(name = "memo")
    @JsonProperty("memo")
    private String memo;

    @Column(name = "tax_reference")
    @JsonProperty("tax_reference")
    private String taxReference;

    @JsonProperty("phase_code")
    @Column(name = "phase_code")
    private String phaseCode;

    @JsonProperty("milestone_code")
    @Column(name = "milestone_code")
    private String milestoneCode;


    /*
    @JsonProperty("activity_code")
    @Column(name = "activity_code")
    private String activityCode;*/

    @JsonProperty("legal_category_code")
    @Column(name = "legal_category_code")
    private String legalCategoryCode;

    @JsonProperty("country_code")
    @Column(name = "country_code")
    private String countryCode;

    @JsonProperty("phase_label")
    @Column(name = "phase_label")
    private String phaseLabel;

    @JsonProperty("milestone_label")
    @Column(name = "milestone_label")
    private String milestoneLabel;


    /*
    @JsonProperty("activity_label")
    @Column(name = "activity_label")
    private String activityLabel;*/

    @JsonProperty("legal_category_label")
    @Column(name = "legal_category_label")
    private String legalCategoryLabel;

    @JsonProperty("country_label")
    @Column(name = "country_label")
    private String countryLabel;

    @JsonProperty("principal_bank_account_number")
    @Column(name = "principal_bank_account_number")
    private String principalBankAccountNumber;

    @JsonProperty("principal_bank_account_title")
    @Column(name = "principal_bank_account_title")
    private String principalBankAccountTitle;

    @JsonProperty("principal_role_code")
    @Column(name = "principal_role_code")
    private String principalRoleCode;

    @JsonProperty("principal_role_label")
    @Column(name = "principal_role_label")
    private String principalRoleLabel;
}
