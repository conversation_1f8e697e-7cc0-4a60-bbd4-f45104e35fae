package com.datatricks.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.util.List;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "timetables")
public class Timetable extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "status")
    @JsonProperty("status")
    private String status;

    @Column(name = "currency_code")
    @JsonProperty("currency_code")
    private String currencyCode;

    @Column(name = "residual_value")
    @JsonProperty("residual_value")
    private Double residualValue;

    @OneToMany(mappedBy = "timetableId", fetch = FetchType.EAGER)
    private List<Level> levels;

    public Timetable(Long id) {
        // For database insertion
        this.id = id;
    }
}
