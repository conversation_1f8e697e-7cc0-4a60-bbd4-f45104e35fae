package com.datatricks.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.util.UUID;


@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "vehicle_stock")
public class VehicleStock {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "reference", unique = true)
    @JsonProperty("reference")
    private UUID reference;

    @Column(name = "brand")
    private String brand;

    @Column(name = "model")
    private String model;

    @Column(name = "style")
    private String style;

    @Column(name = "motor")
    private String motor;

    @Column(name = "finition")
    private String finition;

    @Column(name = "price")
    private Double price;

    @Column(name = "tax_value")
    private Double taxValue;

    @Column(name = "total_price")
    private Double totalPrice;

    @Column(name = "discount")
    private Double discount;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tax_rate")
    private TaxRate taxRate;

    public VehicleStock(UUID vehicleReference) {
        this.reference = vehicleReference;
    }

    public VehicleStock(UUID reference, String bodyLabel, String modelGroupLabel, String s, String s1, String s2,
                        double price, double taxValue, double totalPrice, double discount, TaxRate taxRate) {
        this.reference = reference;
        this.brand = bodyLabel;
        this.model = modelGroupLabel;
        this.style = s;
        this.motor = s1;
        this.finition = s2;
        this.price = price;
        this.taxValue = taxValue;
        this.totalPrice = totalPrice;
        this.discount = discount;
        this.taxRate = taxRate;

    }
}
