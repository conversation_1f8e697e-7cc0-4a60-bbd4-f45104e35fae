package com.datatricks.common.model.dto;

import com.datatricks.common.model.PaymentsType;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SimulationActorPaymentsDto implements PageableDto {

    @JsonProperty("id")
    @Schema(description = "id of the simulation actor payments", example = "1")
    private Long id;

    @JsonProperty("type")
    @Schema(description = "type of the simulation actor payments", example = "Target, Decashment or Collection")
    private PaymentsType type;

    @JsonProperty("name")
    @Schema(description = "name of the simulation actor payments", example = "Simulation Actor Payments 1")
    private String name;

    @JsonProperty("start_date")
    @Schema(description = "start date of the simulation actor payments", example = "2021-01-01")
    private String startDate;

    @JsonProperty("payment_method")
    @Schema(description = "payment method of the simulation actor payments")
    private PaymentMethodDto paymentMethod;

    @JsonProperty("bank_account")
    @Schema(description = "bank account of the simulation actor payments")
    private BankAccountDto bankAccount;

    @JsonProperty("simulation_actor")
    @Schema(description = "simulation actor of the simulation actor payments")
    private SimulationActorResponseDto simulationtActor;
}
