package com.datatricks.common.model;

import com.datatricks.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDate;


@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Getter
@Setter
@Inheritance(strategy = InheritanceType.JOINED) // Change to SINGLE_TABLE or TABLE_PER_CLASS if needed
@DiscriminatorColumn(name = "type")
public class Element extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "condition")
    @Enumerated(EnumType.STRING)
    @JsonProperty("condition")
    private ElementCondition condition;

    @JsonProperty("nature")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "nature", referencedColumnName = "code")
    private StaticParameter nature;

    @Column(name = "price")
    private Double price;

    @Column(name = "total_price")
    private Double totalPrice;

    @Column(name = "serial_number",unique = true)
    @JsonProperty("serial_number")
    private String serialNumber;

    @Column(name = "external_reference")
    @JsonProperty("external_reference")
    private String externalReference;

    @Column(name = "label")
    @JsonProperty("label")
    private String label;

    @Column(name = "description")
    @JsonProperty("description")
    private String description;

    @Column(name = "license")
    @JsonProperty("license")
    private String license;

    @Column(name = "vin")
    @JsonProperty("vin")
    private String vin;

    @Column(name = "short_description")
    @JsonProperty("short_description")
    private String shortDescription;

    @Column(name = "start_date")
    @JsonProperty("start_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate startDate;

    @Column(name = "end_date")
    @JsonProperty("end_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate endDate;

    @Column(name = "registration")
    @JsonProperty("registration")
    private String registration;

    @Column(name = "customer_address_assignment_date")
    @JsonProperty("customer_address_assignment_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate customerAddressAssignmentDate;

    @Column(name = "supplier_address_assignment_date")
    @JsonProperty("supplier_address_assignment_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate supplierAddressAssignmentDate;

    @Column(name = "image_name")
    @JsonProperty("image_name")
    private String imageName;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "amortization_id")
    @JsonProperty("amortization")
    private Amortization amortization;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "phase_id")
    @JsonProperty("phase")
    private Phase phase;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tax_rate_id")
    @JsonProperty("tax_rate")
    private TaxRate taxRate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "milestone_id")
    @JsonProperty("milestone")
    private Milestone milestone;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "currency_id")
    @JsonProperty("currency")
    private Currency currency;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "shipping_id")
    @JsonProperty("shipping")
    private Shipping shipping;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tax_id")
    @JsonProperty("tax")
    private Tax tax;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "supplier_reference", referencedColumnName = "reference")
    @JsonProperty("supplier")
    private Actor supplier;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "nap_code", referencedColumnName = "code")
    @JsonProperty("nap")
    private Category category;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_reference", referencedColumnName = "reference")
    @JsonProperty("order")
    private Order order;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "provider_address_id")
    @JsonProperty("provider_address")
    private Address providerAddress;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "client_reference", referencedColumnName = "reference")
    @JsonProperty("client")
    private Actor client;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "client_address_reference", referencedColumnName = "reference")
    @JsonProperty("client_address")
    private Address clientAddress;

    @PostPersist
    private void generateReference() {
        if (serialNumber == null) {
            long timestamp = System.currentTimeMillis();
            serialNumber = "EL" + id + timestamp;
        }
    }
}

