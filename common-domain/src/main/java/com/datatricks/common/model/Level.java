package com.datatricks.common.model;

import com.datatricks.common.enums.TypeTerme;
import com.datatricks.common.enums.TypeUnitePeriode;
import com.datatricks.common.model.dto.LevelDto;
import com.datatricks.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.util.Date;

@Entity
@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "levels")
public class Level extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "order_number")
    @JsonProperty("order")
    private Integer order;

    @Column(name = "start_date")
    @JsonProperty("start_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date startDate;

    @Column(name = "end_date")
    @JsonProperty("end_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date endDate;

    @Column(name = "due_date")
    @JsonProperty("due_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date dueDate;

    @Column(name = "payment_date")
    @JsonProperty("payment_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date paymentDate;

    @Column(name = "period")
    @JsonProperty("period")
    private TypeUnitePeriode period;

    @Column(name = "period_multiple")
    @JsonProperty("period_multiple")
    private Integer periodMultiple;

    @Column(name = "period_number")
    @JsonProperty("period_number")
    private Integer periodNumber;

    @Column(name = "rent")
    @JsonProperty("rent")
    private Double rent;

    @Column(name = "perception")
    @JsonProperty("perception")
    private TypeTerme perception;

    @Column(name = "rate")
    @JsonProperty("rate")
    private Double rate;

    @Column(name = "nominal_rate")
    @JsonProperty("nominal_rate")
    private Double nominalRate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "timetableId")
    private Timetable timetableId;
}
