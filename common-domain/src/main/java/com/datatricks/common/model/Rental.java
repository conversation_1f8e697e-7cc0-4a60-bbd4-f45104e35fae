package com.datatricks.common.model;

import com.datatricks.common.enums.TypeBase;
import com.datatricks.common.model.dto.RentalDto;
import com.datatricks.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDate;
import java.util.Date;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "rentals")
public class Rental extends BaseEntity implements Rubrique {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "title")
    private String title;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "line_type_code", referencedColumnName = "code")
    private LineType lineType;

    @Column(name = "type")
    @JsonProperty("type")
    private String type;

    @Column(name = "arrangement_type")
    @JsonProperty("arrangement_type")
    private String arrangementType;

    @Column(name = "original_amount")
    @JsonProperty("original_amount")
    private Double originalAmount;

	@Column(name = "initial_amount")
	@JsonProperty("initial_amount")
	private Double initialAmount; //TODO unnecessary field we need to use levels

    @Column(name = "start_date")
    @JsonProperty("start_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate startDate;

    @Column(name = "end_date")
    @JsonProperty("end_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate endDate;

    @Column(name = "effective_date")
    @JsonProperty("effective_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date effectiveDate;

    @Column(name = "nominal_rate")
    @JsonProperty("nominal_rate")
    private Double nominalRate;

    @Column(name = "equivalent_rate")
    @JsonProperty("equivalent_rate")
    private Double equivalentRate;

    @Column(name = "residual_value")
    @JsonProperty("residual_value")
    private Double residualValue;

    @Column(name = "separate_invoice")
    @JsonProperty("separate_invoice")
    private Boolean separateInvoice;

    @Column(name = "suspended_invoice")
    @JsonProperty("suspended_invoice")
    private Boolean suspendedInvoice;

    @Column(name = "mobile_extension")
    @JsonProperty("mobile_extension")
    private Boolean mobileExtension;

    @Setter(AccessLevel.NONE)
    @Column(name = "calculation_basis")
    @JsonProperty("calculation_basis")
    private TypeBase calculationBasis;

    @JsonProperty("calculation_basis")
    public void setCalculationBasisFromString(String base) {
        this.calculationBasis = TypeBase.fromString(base);
    }

    @JsonProperty("calculation_basis")
    public void setCalculationBasis(TypeBase base) {
        this.calculationBasis = base;
    }

    @Setter(AccessLevel.NONE)
    @Column(name = "calculation_mode")
    @JsonProperty("calculation_mode")
    private TypeBase calculationMode;

    @JsonProperty("calculation_mode")
    public void setCalculationModeFromString(String mode) {
        this.calculationMode = TypeBase.fromString(mode);
    }

    @JsonProperty("calculation_mode")
    public void setCalculationMode(TypeBase mode) {
        this.calculationMode = mode;
    }

    @JsonProperty("tax")
    @Column(name = "tax")
    private String tax;

    @JsonProperty("tax_rate")
    @Column(name = "tax_rate")
    private Double taxRate;

    @Column(name = "status")
    @JsonProperty("status")
    @Enumerated(EnumType.STRING)
    private FinancingStatus status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "simulationActorId")
    private SimulationActor simulationActorId;

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "timetableId")
    private Timetable timetableId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "scale_reference", referencedColumnName = "reference")
    private Scale scaleReference;

    public Rental(Long rentalId) {
        this.id = rentalId;
    }
    public Rental(RentalDto rentalDto) {
        this.title = rentalDto.getTitle();
        this.type = rentalDto.getType();
        this.arrangementType = rentalDto.getArrangementType();
        this.originalAmount = rentalDto.getOriginalAmount();
        this.initialAmount = rentalDto.getInitialAmount();
        this.startDate = rentalDto.getStartDate();
        this.endDate = rentalDto.getEndDate();
        this.effectiveDate = rentalDto.getEffectiveDate();
        this.nominalRate = rentalDto.getNominalRate();
        this.equivalentRate = rentalDto.getEquivalentRate();
        this.residualValue = rentalDto.getResidualValue();
        this.separateInvoice = rentalDto.getSeparateInvoice();
        this.suspendedInvoice = rentalDto.getSuspendedInvoice();
        this.mobileExtension = rentalDto.getMobileExtension();
        this.calculationBasis = rentalDto.getCalculationBasis();
        this.calculationMode = rentalDto.getCalculationMode();
        this.tax = rentalDto.getTax();
        this.taxRate = rentalDto.getTaxRate();
        this.status = rentalDto.getStatus();
    }
}