package com.datatricks.common.model;

import com.datatricks.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDate;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(
        name = "simulation_actor_payments",
        uniqueConstraints = @UniqueConstraint(columnNames = {"simulation_actor_id", "type", "deleted_at"}))
public class SimulationActorPayments extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @JsonProperty("type")
    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private PaymentsType type;

    @JsonProperty("start_date")
    @Column(name = "start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate startDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonProperty("payment_method_id")
    private PaymentMethod paymentMethod;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "bank_account_id")
    @JsonProperty("bank_account")
    private BankAccount bankAccount;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonProperty("simulation_actor")
    @JoinColumn(name = "simulation_actor_id")
    private SimulationActor simulationActor;
}
