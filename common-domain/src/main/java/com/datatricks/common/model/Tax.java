package com.datatricks.common.model;

import com.datatricks.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.util.Date;
import java.util.List;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "taxes")
public class Tax extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "code", unique = true)
    private String code;

    @Column(name = "start_date")
    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date startDate;

    @Column(name = "end_date")
    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date endDate;

    @Column(name = "Country")
    private String country;

    @Column(name = "label")
    private String label;

    @Column(name = "active")
    @JsonProperty("active")
    private Boolean active;

    @Column(name = "system_attribute")
    @JsonProperty("system_attribute")
    private Boolean systemAttribute;

    @OneToMany(mappedBy = "tax")
    private List<TaxRate> taxRates;

    public Tax(Long id) {
        this.id = id;
    }

    public Tax(String taxCode) {
        this.code = taxCode;
    }

    public Tax(Long id, String code, String label, Date startDate, Date endDate, String country, Boolean active, Boolean systemAttribute) {
        this.id = id;
        this.code = code;
        this.label = label;
        this.startDate = startDate;
        this.endDate = endDate;
        this.country = country;
        this.active = active;
        this.systemAttribute = systemAttribute;
    }

    public Tax(String code, String label, Date startDate, Date endDate, String country, Boolean active, Boolean systemAttribute) {
        this.code = code;
        this.label = label;
        this.startDate = startDate;
        this.endDate = endDate;
        this.country = country;
        this.active = active;
        this.systemAttribute = systemAttribute;
    }
}