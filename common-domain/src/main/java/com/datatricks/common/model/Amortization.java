package com.datatricks.common.model;

import com.datatricks.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDate;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "amortizations")
public class Amortization extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(name = "name")
    private String name;

    @Column(name = "amortization_law")
    @Enumerated(EnumType.STRING)
    private DepreciationMethod amortizationLaw;

    @Column(name = "start_date")
    @JsonFormat(pattern = DateUtils.DATE_FORMAT)
    private LocalDate startDate;

    @Column(name = "end_date")
    @JsonFormat(pattern = DateUtils.DATE_FORMAT)
    private LocalDate endDate;

    @Column(name = "rental_base")
    private double rentalBase;

    @Column(name = "fiscal_periods")
    private int fiscalPeriods;

    public Amortization(Long id) {
        this.id = id;
    }
}
