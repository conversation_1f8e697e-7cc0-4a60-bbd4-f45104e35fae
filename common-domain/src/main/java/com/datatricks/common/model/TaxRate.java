package com.datatricks.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.util.Date;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "tax_rates")
public class TaxRate extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "code")
    private String code;

    @Column(name = "start_date")
    @JsonProperty("start_date")
    private Date startDate;

    @Column(name = "end_date")
    @JsonProperty("end_date")
    private Date endDate;

    @Column(name = "rate")
    private Double rate;

    @Column(name = "active")
    @JsonProperty("active")
    private Boolean active;

    @Column(name = "system_attribute")
    @JsonProperty("system_attribute")
    private Boolean systemAttribute;

    @Column(name = "label")
    private String label;

    @ManyToOne
    @JoinColumn(name = "tax_code", referencedColumnName = "code")
    private Tax tax;

    public TaxRate(Long id, Tax tax, Double rate, Date startDate, Date endDate, Boolean active, Boolean systemAttribute, String code, String label) {
        this.id = id;
        this.tax = tax;
        this.rate = rate;
        this.startDate = startDate;
        this.endDate = endDate;
        this.active = active;
        this.code = code;
        this.label = label;
        this.systemAttribute = systemAttribute;
    }

    public TaxRate(Tax tax, Double rate, Date startDate, Date endDate, Boolean active, Boolean systemAttribute, String code, String label) {
        this.tax = tax;
        this.rate = rate;
        this.startDate = startDate;
        this.endDate = endDate;
        this.active = active;
        this.systemAttribute = systemAttribute;
        this.code = code;
        this.label = label;
    }
}
