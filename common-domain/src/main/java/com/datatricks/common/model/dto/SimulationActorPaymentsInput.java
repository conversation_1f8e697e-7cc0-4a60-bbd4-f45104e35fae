package com.datatricks.common.model.dto;

import com.datatricks.common.model.PaymentsType;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
public class SimulationActorPaymentsInput {

    @JsonProperty("id")
    @Schema(description = "id of the Simulation actor payments", example = "1")
    private Long id;

    @JsonProperty("type")
    @NotNull(message = "type:please provide a type")
    @Schema(description = "type of the Simulation actor payments", example = "Target, Decashment or Collection")
    private PaymentsType type;

    @JsonProperty("start_date")
    @Schema(description = "start date of the Simulation actor payments", example = "2021-01-01")
    private LocalDate startDate;

    @JsonProperty("payment_method_code")
    @NotNull(message = "payment_method_code:please provide a payment method")
    @Schema(description = "payment method code of the Simulation actor payments")
    private String paymentMethodCode;

    @JsonProperty("bank_account_id")
    @Schema(description = "bank account of the Simulation actor payments")
    private Long bankAccountId;
}
