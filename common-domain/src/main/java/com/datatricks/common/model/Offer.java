package com.datatricks.common.model;

import com.datatricks.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDate;
import java.util.Set;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "offers")
public class Offer extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "reference", unique = true)
    @JsonProperty("reference")
    private String reference;

    /*
     * Foreign keys columns
     */

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "phase_code")
    private Phase phaseCode;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "milestone_code")
    private Milestone milestoneCode;

    @OneToMany(mappedBy = "offer", fetch = FetchType.LAZY)
    private Set<Simulation> simulations;

    public Offer(Long id) {
        this.id = id;
    }
}
