package com.datatricks.common.model;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "product_line_type_mapping")
public class ProductLineTypeMapping {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "product_code", referencedColumnName = "code")
    private Product product;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "operation_nature_type_mapping_code", referencedColumnName = "operation_nature_type_mapping_code")
    private OperationNatureTypeMapping operationNatureTypeMapping;

    public ProductLineTypeMapping(Product product, OperationNatureTypeMapping operationNatureTypeMapping) {
        this.product = product;
        this.operationNatureTypeMapping = operationNatureTypeMapping;
    }

    public String getProductCode() {
        return product.getCode();
    }
}
