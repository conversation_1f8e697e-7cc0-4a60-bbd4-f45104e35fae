package com.datatricks.common.model.dto;

import com.datatricks.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class NewContactDto implements PageableDto {

    @JsonProperty("id")
    private Long id;

    @JsonProperty("title")
    @Schema(description = "Title", example = "Mr, Mrs, Miss, Dr, Prof", type = "string", allowableValues = {"Mr", "Mrs", "Miss", "Dr", "Prof"})
    private String title;

    @JsonProperty("first_name")
    @NotBlank(message = "first_name:please provide a first name")
    @Schema(description = "First name", example = "Ahmed", type = "string")
    private String firstName;

    @JsonProperty("last_name")
    @Schema(description = "Last name", example = "BENZINA", type = "string")
    @NotBlank(message = "last_name:please provide a last name")
    private String lastName;

    @JsonProperty("quality_code")
    @Schema(description = "Quality", example = "SHAREHOLDER, ADMINISTRATOR, ASSISTANT_MANAGER", type = "object")
    private String qualityCode;

    @JsonProperty("preferred")
    @Schema(description = "Preferred", example = "true", type = "boolean")
    private Boolean preferred;

    @JsonProperty("status")
    @Schema(description = "Status", example = "true(ACTIVE), false(INACTIVE)", type = "boolean")
    private boolean status;

    @JsonProperty("start_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Start at", example = "2021-07-01", type = "Date")
    private Date startAt;

    @JsonProperty("birth_day_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Birth day date", example = "2021-07-01", type = "Date")
    private Date birthDayDate;

    @JsonProperty("memo")
    @Schema(description = "Memo", example = "Memo", type = "string")
    private String memo;
}
