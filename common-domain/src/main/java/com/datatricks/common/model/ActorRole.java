package com.datatricks.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDate;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "actor_roles", uniqueConstraints = @UniqueConstraint(columnNames = {"actorId", "role_code", "deleted_at"}))
public class ActorRole extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "is_principal")
    @JsonProperty("is_principal")
    private Boolean isPrincipal;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "actorId")
    private Actor actorId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_code", referencedColumnName = "code")
    private Role role;

    @JsonProperty("invoice_edition_date_limit")
    @Column(name = "invoice_edition_date_limit")
    private LocalDate invoiceEditionDateLimit;

    @JsonProperty("start_date")
    @Column(name = "start_date")
    @Temporal(TemporalType.DATE)
    private LocalDate startDate;

    @JsonProperty("end_date")
    @Column(name = "end_date")
    @Temporal(TemporalType.DATE)
    private LocalDate endDate;

    public ActorRole(Actor actorId, Role role, Boolean isPrincipal) {
        this.actorId = actorId;
        this.role = role;
        this.isPrincipal = isPrincipal;
    }
}
