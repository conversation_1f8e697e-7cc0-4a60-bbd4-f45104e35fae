package com.datatricks.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "payment_methods")
public class PaymentMethod {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @JsonProperty("code")
    @Column(name = "code")
    private String code;

    @JsonProperty("label")
    @Column(name = "label")
    private String label;

    @JsonProperty("language")
    @Column(name = "language")
    private String language;

    @JsonProperty("requires_bank_account")
    @Column(name = "requires_bank_account")
    private Boolean requiresBankAccount;

    public PaymentMethod(Long id) {
        this.id = id;
    }

    public PaymentMethod(String code, String label, String language, Boolean requiresBankAccount) {
        this.code = code;
        this.label = label;
        this.language = language;
        this.requiresBankAccount = requiresBankAccount;
    }
}