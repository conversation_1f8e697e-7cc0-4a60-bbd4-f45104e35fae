package com.datatricks.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "physical_addresses")
public class PhysicalAddress extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "status")
    @JsonProperty("status")
    private boolean status;

    @Column(name = "department")
    @JsonProperty("department")
    private String department;

    @Column(name = "sub_department")
    @JsonProperty("sub_department")
    private String subDepartment;

    @Column(name = "building_name")
    @JsonProperty("building_name")
    private String buildingName;

    @Column(name = "floor")
    @JsonProperty("floor")
    private String floor;

    @Column(name = "room")
    @JsonProperty("room")
    private String room;

    @Column(name = "street_name")
    @JsonProperty("street_name")
    @NotNull(message = "street_name:please provide a street_name")
    private String streetName;

    @Column(name = "building_number")
    @JsonProperty("building_number")
    private String buildingNumber;

    @Column(name = "post_box")
    @JsonProperty("post_box")
    private String postBox;

    @Column(name = "town_location_name")
    @JsonProperty("town_location_name")
    private String townLocationName;

    @Column(name = "post_code")
    @JsonProperty("post_code")
    private String postCode;

    @Column(name = "town_name")
    @JsonProperty("town_name")
    private String townName;

    @Column(name = "country")
    @JsonProperty("country")
    private String country;

    @Column(name = "district_name")
    @JsonProperty("district_name")
    private String districtName;

    @Column(name = "country_sub_division")
    @JsonProperty("country_sub_division")
    private String countrySubDivision;
}
