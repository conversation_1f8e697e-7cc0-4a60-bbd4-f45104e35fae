package com.datatricks.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "delegation")
public class Delegation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "label")
    @JsonProperty("label")
    private String label;

    @Column(name = "short_label")
    @JsonProperty("short_label")
    private String shortLabel;
    @Column(name = "code", unique = true)
    @JsonProperty("code")
    private String code;

	public Delegation(Long id) {
		this.id = id;
	}
}
