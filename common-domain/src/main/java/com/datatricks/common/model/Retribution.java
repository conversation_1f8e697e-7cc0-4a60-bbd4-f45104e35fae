package com.datatricks.common.model;

import com.datatricks.common.enums.TypeBase;
import com.datatricks.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDate;

@Entity

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "retributions")
public class Retribution extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "title")
    private String title;

    @Column(name = "allocation_code")
    @JsonProperty("allocation_code")
    private String allocationCode;

    @Column(name = "arrangement_type")
    @JsonProperty("arrangement_type")
    private String arrangementType;

    @Column(name = "amount")
    @JsonProperty("amount")
    private Double amount;

    @Column(name = "start_date")
    @JsonProperty("start_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate startDate;

    @Column(name = "end_date")
    @JsonProperty("end_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate endDate;

    @Column(name = "separate_invoice")
    @JsonProperty("separate_invoice")
    private Boolean separateInvoice;

    @Column(name = "suspended_invoice")
    @JsonProperty("suspended_invoice")
    private Boolean suspendedInvoice;

    @Setter(AccessLevel.NONE)
    @Column(name = "calculation_basis")
    @JsonProperty("calculation_basis")
    private TypeBase calculationBasis;

    @JsonProperty("calculation_basis")
    public void setCalculationBasisFromString(String base) {
        this.calculationBasis = TypeBase.fromString(base);
    }

    @JsonProperty("calculation_basis")
    public void setCalculationBasis(TypeBase base) {
        this.calculationBasis = base;
    }

    @Setter(AccessLevel.NONE)
    @Column(name = "calculation_mode")
    @JsonProperty("calculation_mode")
    private TypeBase calculationMode;

    @JsonProperty("calculation_mode")
    public void setCalculationModeFromString(String mode) {
        this.calculationMode = TypeBase.fromString(mode);
    }

    @JsonProperty("calculation_mode")
    public void setCalculationMode(TypeBase mode) {
        this.calculationMode = mode;
    }

    @JsonProperty("tax")
    @Column(name = "tax")
    private String tax;

    @JsonProperty("tax_rate")
    @Column(name = "tax_rate")
    private Double taxRate;

	@Column(name = "status")
	@JsonProperty("status")
	private String status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "simulationActorId")
    private SimulationActor simulationActorId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "simulationId")
	private Simulation simulationId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "timetableId")
    private Timetable timetableId;
}
