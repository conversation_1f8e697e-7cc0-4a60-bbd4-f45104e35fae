package com.datatricks.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.util.Set;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "phases",
		uniqueConstraints = @UniqueConstraint(columnNames = {"code", "associated_to"}))
public class Phase {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "code")
    private String code;

    @Column(name = "label")
    private String label;

    @Column(name = "associated_to")
    private String associatedTo;

    @OneToMany(mappedBy = "phaseId", fetch = FetchType.LAZY)
    private Set<Milestone> milestones;

    public Phase(Long id) {
        this.id = id;
    }
}
