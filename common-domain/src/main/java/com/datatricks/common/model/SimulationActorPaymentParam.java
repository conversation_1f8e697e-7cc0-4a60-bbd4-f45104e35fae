package com.datatricks.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import lombok.Getter;

@Getter
public class SimulationActorPaymentParam {

    @JsonProperty("id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "id of the Simulation actor payments", example = "1")
    private Long id;

    @JsonProperty("type")
    @Schema(description = "type of the Simulation actor payments", example = "Target, Decashment or Collection")
    private PaymentsType type;

    @JsonProperty("name")
    @Schema(description = "name of the Simulation actor payments", example = "Simulation Actor Payments 1")
    private String name;

    @JsonProperty("start_date")
    @Schema(description = "start date of the Simulation actor payments", example = "2021-01-01")
    private String startDate;
}
