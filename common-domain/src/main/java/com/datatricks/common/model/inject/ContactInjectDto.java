package com.datatricks.common.model.inject;

<<<<<<< HEAD:common-domain/src/main/java/com/datatricks/common/model/inject/ContactInjectDto.java
import com.datatricks.common.model.Contact;
import com.datatricks.common.model.Positions;
=======
import com.datatricks.common.model.dto.NewContactDto;
>>>>>>> 03a3fc3c7554da2fc8e8b65d661c96bab5f9f678:Offer-module/src/main/java/com/datatricks/offers/inject/dto/ContactDto.java
import com.datatricks.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ContactInjectDto {

    @JsonProperty("id")
    private Long id;

    @JsonProperty("title")
    @Schema(description = "Title", example = "Mr, Mrs, Miss, Dr, Prof", type = "string", allowableValues = {"Mr", "Mrs", "Miss", "Dr", "Prof"})
    private String title;

    @JsonProperty("first_name")
    @Schema(description = "First name", example = "Ahmed", type = "string")
    private String firstName;

    @JsonProperty("last_name")
    @Schema(description = "Last name", example = "BENZINA", type = "string")
    private String lastName;

    @JsonProperty("quality")
<<<<<<< HEAD:common-domain/src/main/java/com/datatricks/common/model/inject/ContactInjectDto.java
    @Schema(description = "Quality", example = "SHAREHOLDER", type = "string")
    private Positions quality;
=======
    @Enumerated(EnumType.STRING)
    @Schema(description = "Quality", example = "SHAREHOLDER, ADMINISTRATOR, ASSISTANT_MANAGER", type = "string")
    private NewContactDto quality;
>>>>>>> 03a3fc3c7554da2fc8e8b65d661c96bab5f9f678:Offer-module/src/main/java/com/datatricks/offers/inject/dto/ContactDto.java

    @JsonProperty("preferred")
    @Schema(description = "Preferred", example = "true", type = "boolean")
    private Boolean preferred;

    @JsonProperty("status")
    @Schema(description = "Status", example = "true(ACTIVE), false(INACTIVE)", type = "boolean")
    private boolean status;

    @JsonProperty("start_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Start at", example = "2021-07-01", type = "Date")
    private Date startAt;

    @JsonProperty("birth_day_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Birth day date", example = "2021-07-01", type = "Date")
    private Date birthDayDate;

    @JsonProperty("memo")
    @Schema(description = "Memo", example = "Memo", type = "string")
    private String memo;

    @JsonProperty("communication_means")
    private List<CommunicationMeanInjectDto> communicationMeans;

    public ContactInjectDto(Contact contact) {
        this.id = contact.getId();
        this.title = contact.getTitle();
        this.firstName = contact.getFirstName();
        this.lastName = contact.getLastName();
        this.quality = contact.getQuality();
        this.preferred = contact.getPreferred();
        this.status = contact.isStatus();
        this.startAt = contact.getStartAt();
        this.birthDayDate = contact.getBirthDayDate();
        this.memo = contact.getMemo();
        this.communicationMeans = contact.getCommunicationMeans() != null ? contact.getCommunicationMeans().stream().map(CommunicationMeanInjectDto::new).toList() : null;
    }
}
