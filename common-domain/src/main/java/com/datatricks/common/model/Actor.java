package com.datatricks.common.model;

import com.datatricks.common.model.dto.NewActorDto;
import com.datatricks.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.*;

import java.util.Date;
import java.util.Set;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "actors")
public class Actor extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "reference", unique = true)
    @JsonProperty("reference")
    private String reference;

    @Column(name = "external_reference")
    @JsonProperty("external_reference")
    private String externalReference;

    @Column(name = "short_name")
    @JsonProperty("short_name")
    @NotBlank(message = "short_name:please provide a short name")
    private String shortName;

    @Column(name = "name")
    @JsonProperty("name")
    @NotBlank(message = "name:please provide a name")
    private String name;

    @Column(name = "vat")
    @JsonProperty("vat")
    private String vat;

    @Column(name = "registration_date")
    @JsonProperty("registration_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date registrationDate;

    @Column(name = "company_creation_date")
    @JsonProperty("company_creation_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date companyCreationDate;

    @Column(name = "registration_country")
    @JsonProperty("registration_country")
    private String registrationCountry;

    @Column(name = "register_type")
    @JsonProperty("registerType")
    private String registerType;

    @Column(name = "register_number")
    @JsonProperty("register_number")
    private String registerNumber;

    @Column(name = "type")
    @JsonProperty("type")
    @Enumerated(EnumType.STRING)
    private ActorTypes type;

    @Column(name = "national_identity", unique = true)
    @JsonProperty("national_identity")
    @NotBlank(message = "national_identity:please provide a national identity")
    private String nationalIdentity;

    @Column(name = "feed_channel")
    @JsonProperty("feed_channel")
    private String feedChannel;

    @Column(name = "memo")
    @JsonProperty("memo")
    private String memo;

    @Column(name = "tax_reference")
    @JsonProperty("tax_reference")
    private String taxReference;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "phaseId")
    private Phase phase;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "milestoneId")
    private Milestone milestone;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "legal_activity_code", referencedColumnName = "code")
    private LegalActivities activity;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "legalCategoryId")
    private LegalCategory legalCategory;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "countryId")
    private Country country;

    @OneToMany(mappedBy = "actorId", fetch = FetchType.LAZY, cascade = CascadeType.ALL , orphanRemoval = true)
    private Set<ActorRole> actorRole;

    @OneToMany(mappedBy = "actorId", fetch = FetchType.LAZY)
    private Set<BankAccount> bankAccounts;

    @OneToMany(mappedBy = "actor", fetch = FetchType.LAZY)
    private Set<Address> addresses;

    @OneToMany(mappedBy = "actorId", fetch = FetchType.LAZY)
    private Set<Contact> contacts;

    @OneToMany(mappedBy = "actor", fetch = FetchType.LAZY)
    private Set<SimulationActor> simulationActor;

    public Actor(Long id) {
        this.id = id;
    }

    public Actor(String actorReference) {
        this.reference = actorReference;
    }

    public Actor(NewActorDto newActorDto) {
        this.externalReference = newActorDto.getExternalReference();
        this.shortName = newActorDto.getShortName();
        this.name = newActorDto.getName();
        this.vat = newActorDto.getVat();
        this.registrationDate = newActorDto.getRegistrationDate();
        this.companyCreationDate = newActorDto.getCompanyCreationDate();
        this.registrationCountry = newActorDto.getRegistrationCountry();
        this.registerType = newActorDto.getRegisterType();
        this.registerNumber = newActorDto.getRegisterNumber();
        this.type = newActorDto.getType();
        this.nationalIdentity = newActorDto.getNationalIdentity();
        this.feedChannel = newActorDto.getFeedChannel();
        this.memo = newActorDto.getMemo();
        this.taxReference = newActorDto.getTaxReference();
    }
}
