package com.datatricks.common.model;

import com.datatricks.common.model.dto.PageableDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.*;

import java.util.List;
import java.util.Set;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "simulations")
public class Simulation extends BaseEntity implements PageableDto {

    @Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

	@Column(name = "reference", unique = true)
	@JsonProperty("reference")
	private String reference;

    @Column(name = "title")
    @JsonProperty("title")
    @NotBlank(message = "title:please provide a title")
    private String title;

	@Column(name = "status")
	@JsonProperty("status")
	private String status;

	@Column(name = "total_price_ht")
	private Double totalPriceHt;

	@Column(name = "total_price_ttc")
	private Double totalPriceTtc;

	@Column(name = "total_quantity")
	private Integer totalQuantity;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "nature_code", referencedColumnName = "code")
	private StaticParameter nature;

	@Column(name = "actor_type")
	@JsonProperty("actor_type")
	@Enumerated(EnumType.STRING)
	private ActorTypes actorType;

	@Column(name = "feed_channel")
	@JsonProperty("feed_channel")
	private String feedChannel;

	/*
     * Foreign keys columns
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "offer_reference", referencedColumnName = "reference")
    private Offer offer;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "productId")
	private Product product;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "business_reference", referencedColumnName = "reference")
	private Actor businessReference;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "activity_id", referencedColumnName = "id")
	private Activity activity;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "legal_activity_code", referencedColumnName = "code")
	private LegalActivities legalactivity;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "phase_code")
	private Phase phaseCode;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "milestone_code")
	private Milestone milestoneCode;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "scale_reference", referencedColumnName = "reference")
	private Scale scaleReference;

	@OneToMany(mappedBy = "simulation", fetch = FetchType.LAZY)
	private Set<SimulationActor> simulationActor;

	@OneToMany(mappedBy = "simulation", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
	private List<SimulationElement> simulationElements;

    public Simulation(Long id) {
        this.id = id;
    }

	@PostPersist
	private void generateReference() {
		if (reference == null) {
			long timestamp = System.currentTimeMillis();
			reference = "SM" + id + timestamp;
		}
	}
}
