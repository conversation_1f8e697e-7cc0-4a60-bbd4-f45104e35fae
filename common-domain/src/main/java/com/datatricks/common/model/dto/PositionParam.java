package com.datatricks.common.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PositionParam implements PageableDto {

    @JsonProperty("id")
    @Schema(description = "Position ID", example = "1", type = "integer")
    private Long id;

    @JsonProperty("code")
    @Schema(description = "Country code", example = "FR", type = "string")
    private String code;

    @JsonProperty("label")
    @Schema(description = "Country label", example = "France", type = "string")
    private String label;

    @JsonProperty("language")
    @Schema(description = "Language", example = "fr", type = "string")
    private String language;

    @JsonProperty("name")
    @Schema(description = "Name", example = "Directeur Général", type = "string")
    private String name;
}
