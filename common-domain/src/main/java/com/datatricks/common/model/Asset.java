package com.datatricks.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.util.Set;

@Entity

@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "assets")
public class Asset extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "reference", unique = true)
    @JsonProperty("reference")
    private String reference;

    @Column(name = "label")
    @JsonProperty("label")
    private String label;

    @Column(name = "order_number")
    @JsonProperty("order_number")
    private int orderNumber;

    @Column(name = "description")
    @JsonProperty("description")
    private String description;

    @Column(name = "total_value")
    @JsonProperty("total_value")
    private double totalValue;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id")
    @JsonProperty("company")
    private Actor company;

    /*@OneToMany(mappedBy = "asset")
    @JsonProperty("elements")
    private Set<Element> elements;*/

    public Asset(Long assetId) {
        this.id = assetId;
    }
}
