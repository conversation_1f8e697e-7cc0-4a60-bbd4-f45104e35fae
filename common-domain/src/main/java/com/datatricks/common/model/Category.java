package com.datatricks.common.model;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "categories")
public class Category extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "country_code")
    private String countryCode;

    @Column(name = "code")
    private String code;

    @Column(name = "language")
    private String language;

    @Column(name = "label")
    private String label;

    public Category(Long id) {
        this.id = id;
    }

    public Category(String categoryCode) {
        this.code = categoryCode;
    }
}
