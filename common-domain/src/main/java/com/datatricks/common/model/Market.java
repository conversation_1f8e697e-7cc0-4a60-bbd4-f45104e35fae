package com.datatricks.common.model;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "markets")
public class Market extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "code", unique = true)
    private String code;

    @Column(name = "label")
    private String label;

    @Column(name = "description")
    private String description;

    @Column(name = "system_attribute")
    private Boolean systemAttribute;

    @Column(name = "active")
    private Boolean active;

    public Market(String marketCode) {
        this.code = marketCode;
    }
}
