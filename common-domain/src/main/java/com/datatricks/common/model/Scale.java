package com.datatricks.common.model;

import com.datatricks.common.enums.ScaleStatus;
import com.datatricks.common.enums.TypeUnitePeriode;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDate;
import java.util.Set;
import java.util.UUID;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "scales")
@Inheritance(strategy = InheritanceType.JOINED)
@DiscriminatorColumn(name = "type", discriminatorType = DiscriminatorType.STRING)
public class Scale extends BaseEntity {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@JsonProperty("id")
	private Long id;

	@Column(name = "code", unique = true)
	@JsonProperty("code")
	private String code;

	@Column(name = "reference", unique = true)
	@JsonProperty("reference")
	private UUID reference;

	@Column(name = "start_date")
	@JsonProperty("start_date")
	private LocalDate startDate;

	@Column(name = "end_date")
	@JsonProperty("end_date")
	private LocalDate endDate;

	@Column(name = "description")
	@JsonProperty("description")
	private String description;

	@Column(name = "label")
	@JsonProperty("label")
	private String label;

	@Column(name = "maximum_eligible_amount")
	@JsonProperty("maximum_eligible_amount")
	private Double maximumEligibleAmount;

	@Column(name = "minimum_eligible_amount")
	@JsonProperty("minimum_eligible_amount")
	private Double minimumEligibleAmount;

	@Column(name = "rate_period")
	@JsonProperty("rate_period")
	@Enumerated(EnumType.STRING)
	private TypeUnitePeriode ratePeriod;

	@Column(name = "maximum_financing_period")
	@JsonProperty("maximum_financing_period")
	private Integer maximumFinancingPeriod;

	@Column(name = "minimum_financing_period")
	@JsonProperty("minimum_financing_period")
	private Integer minimumFinancingPeriod;

	@Column(name = "maximum_residual_value")
	@JsonProperty("maximum_residual_value")
	private Double maximumResidualValue;

	@Column(name = "minimum_residual_value")
	@JsonProperty("minimum_residual_value")
	private Double minimumResidualValue;

	@Column(name = "maximum_personal_contribution")
	@JsonProperty("maximum_personal_contribution")
	private Double maximumPersonalContribution;

	@Column(name = "minimum_personal_contribution")
	@JsonProperty("minimum_personal_contribution")
	private Double minimumPersonalContribution;

	@Column(name = "maximum_security_deposit")
	@JsonProperty("maximum_security_deposit")
	private Double maximumSecurityDeposit;

	@Column(name = "minimum_security_deposit")
	@JsonProperty("minimum_security_deposit")
	private Double minimumSecurityDeposit;

	@Column(name = "asset_usage")
	@JsonProperty("asset_usage")
	@Enumerated(EnumType.STRING)
	private UsageTypes assetUsage;

	@Column(name = "condition")
	@JsonProperty("condition")
	@Enumerated(EnumType.STRING)
	private ElementCondition condition;

	@JsonProperty("nature")
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "nature", referencedColumnName = "code")
	private StaticParameter nature;

	@Column(name = "minimum_interest_rate")
	@JsonProperty("minimum_interest_rate")
	private Double minimumInterestRate;

	@Column(name = "maximum_interest_rate")
	@JsonProperty("maximum_interest_rate")
	private Double maximumInterestRate;

	@Column(name = "nominal_interest_rate")
	@JsonProperty("nominal_interest_rate")
	private Double nominalInterestRate;

	@Column(name = "has_personal_contribution")
	@JsonProperty("has_personal_contribution")
	private Boolean hasPersonalContribution;

	@Column(name = "has_security_deposit")
	@JsonProperty("has_security_deposit")
	private Boolean hasSecurityDeposit;

	@Column(name = "grace_period_duration")
	@JsonProperty("grace_period_duration")
	private Integer gracePeriodDuration;

	@Column(name = "with_interest_payment")
	@JsonProperty("with_interest_payment")
	private Boolean withInterestPayment;

	@Column(name = "has_grace_period")
	@JsonProperty("has_grace_period")
	private Boolean hasGracePeriod;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "currency", referencedColumnName = "code")
	private Currency currency;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "market_code", referencedColumnName = "code")
	private Market market;

	@Column(name = "status")
	@JsonProperty("status")
	@Enumerated(EnumType.STRING)
	private ScaleStatus status;

	@OneToOne
	@JoinColumn(name = "application_criteria_reference", referencedColumnName = "reference")
	private ApplicationCriteria applicationCriteria;

	@OneToMany(mappedBy = "scale", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	private Set<ScaleFinancialProduct> scaleFinancialProducts;

	@OneToMany(mappedBy = "scale", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	private Set<ScaleCommercialProduct> scaleCommercialProducts;

	@OneToMany(mappedBy = "scale", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	private Set<ScaleService> scaleServices;

	@OneToMany(mappedBy = "scale", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	private Set<ThirdParty> thirdParties;

	public Scale(String scaleCode) {
		this.code = scaleCode;
	}
}
