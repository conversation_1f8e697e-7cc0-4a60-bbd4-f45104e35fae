package com.datatricks.common.model;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "operation_nature_type_mapping")
public class OperationNatureTypeMapping {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "operation_nature_type_mapping_code", unique = true)
    private String operationNatureTypeMappingCode;

    @ManyToOne
    @JoinColumn(name = "operation_nature_mapping_code", referencedColumnName = "operation_nature_code")
    private OperationNatureMapping operationNatureMapping;

    @ManyToOne
    @JoinColumn(name = "type_code", referencedColumnName = "code")
    private LineType type;

    @Column(name = "type_status")
    private Boolean typeStatus;

    public OperationNatureTypeMapping(Long operationNatureTypeMappingId) {
        this.id = operationNatureTypeMappingId;
    }

    public OperationNatureTypeMapping(String operationNatureTypeMappingCode) {
        this.operationNatureTypeMappingCode = operationNatureTypeMappingCode;
    }

    public OperationNatureTypeMapping(String operationNatureTypeCode, OperationNatureMapping operationNatureMapping, LineType lineType, Boolean typeStatus) {
        this.operationNatureTypeMappingCode = operationNatureTypeCode;
        this.operationNatureMapping = operationNatureMapping;
        this.type = lineType;
        this.typeStatus = typeStatus;
    }
}
