package com.datatricks.common.model;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "equipments")
public class Equipment extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "code", unique = true)
    private String code;

    @Column(name = "label")
    private String label;

    @Column(name = "description")
    private String description;

    @Column(name = "system_attribute")
    private Boolean systemAttribute;

    @Column(name = "active")
    private Boolean active;

    @ManyToOne
    @JoinColumn(name = "market_code", referencedColumnName = "code")
    private Market market;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_code", referencedColumnName = "code")
    private Category category;

    @ManyToOne
    @JoinColumn(name = "country_code", referencedColumnName = "code")
    private Country country;

    public Equipment(String equipmentCode) {
        this.code = equipmentCode;
    }

    public Equipment(String code, String label, String description, Boolean systemAttribute, Boolean active, Market market, Category category, Country country) {
        this.code = code;
        this.label = label;
        this.description = description;
        this.systemAttribute = systemAttribute;
        this.active = active;
        this.market = market;
        this.category = category;
        this.country = country;
    }
}
