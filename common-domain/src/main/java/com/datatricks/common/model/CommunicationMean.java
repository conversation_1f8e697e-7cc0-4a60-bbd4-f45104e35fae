package com.datatricks.common.model;

import com.datatricks.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.util.Date;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "communication_means")
public class CommunicationMean extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "type")
    @JsonProperty("type")
    @NotNull(message = "type:please provide a type")
    @Enumerated(EnumType.STRING)
    private CommunicationMeanTypes type;

    @Column(name = "reference")
    @JsonProperty("reference")
    @NotBlank(message = "reference:please provide a reference")
    private String reference;

    @Column(name = "preferred")
    @JsonProperty("preferred")
    private Boolean preferred;

    @Column(name = "status")
    @JsonProperty("status")
    private boolean status;

    @Column(name = "start_date")
    @JsonProperty("start_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date startDate;

    @Column(name = "end_date")
    @JsonProperty("end_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date endDate;

    @Column(name = "last_contact_date")
    @JsonProperty("last_contact_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date lastContactDate;

    @ManyToOne
    @JoinColumn(name = "contactId")
    private Contact contactId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "billingParameters_id")
    private BillingParameters billingParameters;
}