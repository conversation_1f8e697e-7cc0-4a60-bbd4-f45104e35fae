package com.datatricks.common.model;

import com.datatricks.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.*;

import java.util.Date;
import java.util.Set;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "contacts")
public class Contact extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "title")
    @JsonProperty("title")
    private String title;

    @Column(name = "first_name")
    @JsonProperty("first_name")
    @NotBlank(message = "first_name:please provide a first name")
    private String firstName;

    @Column(name = "last_name")
    @JsonProperty("last_name")
    @NotBlank(message = "last_name:please provide a last name")
    private String lastName;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "quality_code", referencedColumnName = "code")
    @JsonProperty("quality_code")
    private Position quality;

    @Column(name = "preferred")
    @JsonProperty("preferred")
    private Boolean preferred;

    @Column(name = "start_at")
    @JsonProperty("start_at")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date startAt;

    @Column(name = "birth_day_date")
    @JsonProperty("birth_day_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date birthDayDate;

    @Column(name = "memo")
    @JsonProperty("memo")
    private String memo;

    @Column(name = "status")
    @JsonProperty("status")
    private boolean status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "actorId")
    private Actor actorId;

    @OneToMany(mappedBy = "contactId", fetch = FetchType.LAZY)
    private Set<CommunicationMean> communicationMeans;
}
