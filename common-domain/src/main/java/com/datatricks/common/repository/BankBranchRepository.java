package com.datatricks.common.repository;

import com.datatricks.common.model.BankBranch;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;
import java.util.UUID;

public interface BankBranchRepository extends JpaRepository<BankBranch, Long>, JpaSpecificationExecutor<BankBranch> {
    BankBranch findByInterbankCodeAndDeletedAtIsNull(String interbankCode);

    Optional<BankBranch> findByReferenceAndDeletedAtIsNull(@NotEmpty(message = "reference: reference cannot be empty") UUID reference);
}
