package com.datatricks.common.repository;

import com.datatricks.common.model.ScaleElementVehicle;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import java.util.Optional;

import java.util.UUID;

public interface ScaleElementVehicleRepository extends JpaRepository<ScaleElementVehicle, Long>, JpaSpecificationExecutor<ScaleElementVehicle> {
    Optional<ScaleElementVehicle> findByCodeAndDeletedAtIsNull(String code);

    Optional<ScaleElementVehicle> findByReferenceAndDeletedAtIsNull(UUID reference);
}
