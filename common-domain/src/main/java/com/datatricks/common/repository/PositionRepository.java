package com.datatricks.common.repository;

import com.datatricks.common.model.Position;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import java.util.Optional;

public interface PositionRepository extends JpaRepository<Position, Long>, JpaSpecificationExecutor<Position> {
    Optional<Position> findByCodeAndDeletedAtIsNull(String code);
}
