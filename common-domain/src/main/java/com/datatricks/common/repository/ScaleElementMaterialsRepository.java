package com.datatricks.common.repository;

import com.datatricks.common.model.ScaleElementMaterial;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;
import java.util.UUID;

public interface ScaleElementMaterialsRepository extends JpaRepository<ScaleElementMaterial, Long> {
    Optional<ScaleElementMaterial> findByCodeAndDeletedAtIsNull(String code);

    Optional<ScaleElementMaterial> findByReferenceAndDeletedAtIsNull(UUID reference);
    // Custom query methods can be defined here if needed
}
