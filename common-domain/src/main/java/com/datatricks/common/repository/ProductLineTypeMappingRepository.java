package com.datatricks.common.repository;

import com.datatricks.common.model.ProductLineTypeMapping;
import jakarta.validation.constraints.NotBlank;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface ProductLineTypeMappingRepository extends JpaRepository<ProductLineTypeMapping, Long> {
    Optional<ProductLineTypeMapping> findByProduct_CodeAndOperationNatureTypeMappingId(@NotBlank(message = "please provide a product code") String productCode,
                                                                                       Long attr0);

    void deleteByOperationNatureTypeMappingId(Long id);

    List<ProductLineTypeMapping> findByProduct_Code(@NotBlank(message = "please provide a product code") String code);

    Collection<ProductLineTypeMapping> findByProduct_CodeIn(ArrayList<String> strings);

    void deleteByOperationNatureTypeMappingOperationNatureTypeMappingCode(String id);

    void deleteByProduct_CodeAndOperationNatureTypeMappingOperationNatureTypeMappingCode(@NotBlank(message = "please provide a product code") String code, String operationNatureTypeMappingCode);

    void deleteByProduct_Code(@NotBlank(message = "please provide a product code") String code);
}
