package com.datatricks.common.repository;

import com.datatricks.common.model.Activity;
import com.datatricks.common.model.ActorActivityProduct;
import jakarta.validation.constraints.NotBlank;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ActorActivityProductRepository extends JpaRepository<ActorActivityProduct, Long>, JpaSpecificationExecutor<ActorActivityProduct> {
    @Query("SELECT DISTINCT aap.activity FROM ActorActivityProduct aap WHERE aap.actor.reference = :actorReference")
    List<Activity> findActivitiesByActorReference(@Param("actorReference") String actorReference);

    @Query("SELECT aap FROM ActorActivityProduct aap " +
            "WHERE aap.actor.reference = :actorReference " +
            "AND aap.activity.code = :activityCode")
    List<ActorActivityProduct> findByActorReferenceAndActivityCode(
            @Param("actorReference") String actorReference,
            @Param("activityCode") String activityCode
    );

    ActorActivityProduct findByActorReferenceAndActivityCodeAndProductCode(String actorReference, String activityCode, String productCode);

    List<ActorActivityProduct> findByActorReferenceAndActivityCodeIn(String actorReference, List<String> activityCodes);

    List<ActorActivityProduct> findByActivityCode(String activityCode);

    List<ActorActivityProduct> findByActivityCodeAndProductCode(String activityCode, String productCode);

    void deleteAllByActivityCodeAndProductCode(String code, @NotBlank(message = "please provide a product code") String code1);
}
