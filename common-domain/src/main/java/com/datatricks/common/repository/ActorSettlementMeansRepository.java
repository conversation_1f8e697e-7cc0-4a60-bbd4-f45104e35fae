package com.datatricks.common.repository;

import com.datatricks.common.model.PaymentsType;
import com.datatricks.common.model.SettlementMeans;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

public interface ActorSettlementMeansRepository extends JpaRepository<SettlementMeans, Long>, JpaSpecificationExecutor<SettlementMeans> {
    Optional<SettlementMeans> findByActorIdAndRoleCodeAndPaymentTypeAndDeletedAtIsNull(Long idActor, String roleCode, PaymentsType type);

    List<SettlementMeans> findAllByActorIdAndRoleCodeAndDeletedAtIsNull(Long idActor, String roleCode);

    Optional<SettlementMeans> findByIdAndActorId(Long paymentMethodId, Long actorId);

    Optional<SettlementMeans> findByIdAndActorReference(Long paymentMethodId, String reference);

    List<SettlementMeans>  findByActorReferenceAndRoleCodeAndDeletedAtIsNull(String reference, String roleCode);

    Optional<SettlementMeans> findByActorReferenceAndRoleCodeAndPaymentTypeAndDeletedAtIsNull(String reference, String roleCode, PaymentsType type);

    Optional<SettlementMeans> findByActorReferenceAndPaymentTypeAndDeletedAtIsNull(String actorReference, PaymentsType type);

    List<SettlementMeans> findByActorIdAndDeletedAtIsNull(Long actorId);
}
