package com.datatricks.common.repository;

import com.datatricks.common.model.PaymentsType;
import com.datatricks.common.model.SimulationActorPayments;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

public interface SimulationActorPaymentsRepository
        extends JpaRepository<SimulationActorPayments, Long>, JpaSpecificationExecutor<SimulationActorPayments> {

    List<SimulationActorPayments> findBySimulationActorId(Long id);

    List<SimulationActorPayments> findBySimulationActorIdAndDeletedAtIsNull(Long simulationId);

    Optional<SimulationActorPayments> findByIdAndDeletedAtIsNull(Long id);

    Optional<SimulationActorPayments> findBySimulationActorIdAndTypeAndDeletedAtIsNull(Long simulationId, PaymentsType type);
}
