package com.datatricks.common.utils;

import com.datatricks.common.model.*;
import com.datatricks.common.model.dto.JpaQueryDto;

import java.util.Map;

public class JpaQueryUtils {

    private JpaQueryUtils() {
        // Utility class
    }

    // This class MUST remain immutable
    // spotless:off
    // @formatter:off

    // [         KEY      : [       KEY     : [                     VALUE1                     |              VALUE2
    //           |    VALUE3     |  VALUE4]]
    // [ Principal entity : [Param from URL : [ joinColumn between Principal and joined entity | column to filter in
    // joined entity | Joined entity | next join]]

    private static final Map<Class<?>, Map<String, JpaQueryDto>> correspondanceEntityMap =  Map.ofEntries(
            Map.entry(Element.class, Map.of(
                    "phase_code", new JpaQueryDto("phase", "code", Phase.class, null),
                    "Simulation_elements", new JpaQueryDto("SimulationElementId", "Simulation_id", SimulationElement.class, null),
                    "company_Simulation_elements", new JpaQueryDto("SimulationElementId", "actor_id", SimulationElement.class, null),
                    "supplier_reference", new JpaQueryDto("supplier", "reference", Actor.class, null),
                    "category_code", new JpaQueryDto("category", "code", Category.class, null),
                    "nature_code", new JpaQueryDto("nature", "code", StaticParameter.class, null)
               )),
            Map.entry(SimulationElement.class, Map.of(
                    "actor_id", new JpaQueryDto("actor", "id", Actor.class, null),
                    "Simulation_id", new JpaQueryDto("Simulation", "id", Simulation.class, null),
                    "join_Simulation_elements", new JpaQueryDto("Simulation", "id", Simulation.class, "join_simulation")
            )),
            Map.entry(Simulation.class, Map.of(
                    "join_simulation", new JpaQueryDto("id", "Simulation_id", SimulationElement.class, "join_simulation_elements"),
                    "nature_code", new JpaQueryDto("nature", "code", StaticParameter.class, null),
                    "business_short_name", new JpaQueryDto("businessReference", "short_name", Actor.class, null),
                    "simulation_actor_short_name", new JpaQueryDto("simulationActor", "actor", SimulationActor.class, "join_simulation_actor_name"),
                    "simulation_offer_reference", new JpaQueryDto("offer", "reference", Offer.class, null)
            )),
            Map.entry(Actor.class,
            Map.ofEntries(
                    Map.entry("role_id", new JpaQueryDto("actorRole", "role_id", ActorRole.class, null)),
                    Map.entry("role_is_principal", new JpaQueryDto("actorRole", "is_principal", ActorRole.class, null)),
                    Map.entry("phase_code", new JpaQueryDto("phase", "code", Phase.class, null)),
                    Map.entry("phase_associated_to", new JpaQueryDto("phase", "associated_to", Phase.class, null)),
                    Map.entry("offer_reference", new JpaQueryDto("simulationActor", "offer_id", SimulationActor.class, "join_offer_reference")),
                    Map.entry("role_code", new JpaQueryDto("actorRole", "role_code", ActorRole.class, "join_role_code")),
                    Map.entry("offer_id", new JpaQueryDto("simulationActor", "offer_id", SimulationActor.class, "join_offer_id")),
                    Map.entry("milestone_code", new JpaQueryDto("milestone", "code", Milestone.class, null)),
                    Map.entry("milestone_label", new JpaQueryDto("milestone", "label", Milestone.class, null)),
                    Map.entry("role_label", new JpaQueryDto("actorRole", "role_code", ActorRole.class, "join_role_label")),
                    Map.entry("phase_label", new JpaQueryDto("phase", "label", Phase.class, null)),
                    Map.entry("join_actor_role", new JpaQueryDto("actorRole", "role_id", ActorRole.class, null))
            )),
            Map.entry(SimulationActor.class,
            Map.of(
                    "actor_id", new JpaQueryDto("actor", "id", Actor.class, null),
                    "offer_id", new JpaQueryDto("offer", "id", Offer.class, null),
                    "join_offer_reference", new JpaQueryDto("offerId", "reference", Offer.class, null),
                    "join_offer_id", new JpaQueryDto("offerId", "id", Offer.class, null),
                    "join_actor_name", new JpaQueryDto("actorId", "name", Actor.class, null),
                    "join_actor_identity", new JpaQueryDto("actorId", "national_identity", Actor.class, null),
                    "join_simulation_actor_role",
                    new JpaQueryDto("actorId", "role_id", Actor.class, "join_actor_role"),
                    "join_actor_code", new JpaQueryDto("actorId", "reference", Actor.class, null),
                    "join_simulation_actor_role_id",
                    new JpaQueryDto("actor_id", "role_id", SimulationActor.class, null),
                    "join_simulation_actor_name", new JpaQueryDto("actor", "short_name", Actor.class, null)
            )),Map.entry(FiscalYear.class,
            Map.of(
                    "actor_id", new JpaQueryDto("actor", "id", Actor.class, null)
            )),
            Map.entry(ActorRole.class, Map.of(
                    "join_role_code", new JpaQueryDto("role", "code", Role.class, null),
                    "join_role_label", new JpaQueryDto("role", "label", Role.class, null)
            )),
            Map.entry(Role.class, Map.of(
                    "join_role_id", new JpaQueryDto("id", "code", Role.class, null)
            )),
            Map.entry(Offer.class,
            Map.of(
                    "simulation_actor", new JpaQueryDto("simulationActor", "actor_id", SimulationActor.class, null),
                    "simulation_actor_name",
                    new JpaQueryDto(
                            "simulationActor", "actor_id", SimulationActor.class, "join_actor_name"),
                    "simulation_actor_identity",
                    new JpaQueryDto(
                            "simulationActor", "actor_id", SimulationActor.class, "join_actor_identity"),
                    "simulation_actor_role",
                    new JpaQueryDto(
                            "simulationActor",
                            "actor_id",
                            SimulationActor.class,
                            "join_simulation_actor_role"),
                    "phase_code", new JpaQueryDto("phaseId", "code", Phase.class, null),
                    "simulation_actor_code",
                    new JpaQueryDto(
                            "simulationActor", "actor_id", SimulationActor.class, "join_actor_code"),
                    "simulationActor_role",
                    new JpaQueryDto(
                            "simulationActor", "role_id", SimulationActor.class, null)
            )),Map.entry(Rental.class, Map.of("offer_Actor", new JpaQueryDto("simulationActorId", "id", SimulationActor.class, null),
                    "offer", new JpaQueryDto("simulationActorId", "offer_id", SimulationActor.class, null)
            )),Map.entry(Accessory.class, Map.of("offer_Actor", new JpaQueryDto("simulationActorId", "id", SimulationActor.class, null))),
                    Map.entry(Repayment.class, Map.of(
                    "rental", new JpaQueryDto("rentalId", "PEqual_id", Rental.class, null),
                    "accessory", new JpaQueryDto("accessoryId", "PEqual_id", Accessory.class, null),
                    "timetable", new JpaQueryDto("timetableId", "PEqual_id", Timetable.class, null),
                    "simulation_actor", new JpaQueryDto("simulationActorId", "PEqual_id", SimulationActor.class, null)
            ))
            );
    // @formatter:on
    // spotless:off
    public static JpaQueryDto getParameterMapping(Class<?> clazz, String param) {
        Map<String, JpaQueryDto> entityMap = correspondanceEntityMap.get(clazz);
        if (entityMap != null) {
            return entityMap.get(param);
        } else {
            return null;
        }
    }
}
