<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
   <changeSet id="KF-237-create-custom-sequences" author="ahmed khiari">
       <sql splitStatements="false">
           DO
           $$
    DECLARE
           table_list text[] := ARRAY[
            'dt_accessories', 'dt_activities', 'dt_billing_parameters',
            'dt_categories', 'dt_communication_means', 'dt_countries',
            'dt_currencies', 'dt_delegation', 'dt_line_types',
            'dt_material', 'dt_milestones', 'dt_natures',
            'dt_parameters', 'dt_payment_methods', 'dt_phases',
            'dt_product_line_type_mapping', 'dt_products', 'dt_roles',
            'dt_tax_rates', 'dt_taxes', 'dt_vehicle', 'dt_vehicle_stock'
            ];
        current_table
           text;
        max_id
           bigint;
        seq_name
           text;
        random_suffix
           text;
           BEGIN
        FOREACH
           current_table IN ARRAY table_list LOOP
           BEGIN
                    -- Generate random 5-character suffix (letters and numbers)
           SELECT string_agg(substr('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',
                                    (ceil(random() * 36)):: integer, 1), '')
           FROM generate_series(1, 5) INTO random_suffix;

           -- Create sequence name with random suffix
           seq_name
           := lower(current_table || '_seq_' || random_suffix);

                    -- Get max ID from table
           EXECUTE format('SELECT COALESCE(MAX(id), 0) + 1 FROM %I', current_table) INTO max_id;

           -- Create sequence starting from max_id
           EXECUTE format('CREATE SEQUENCE %I START WITH %s INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1',
                          seq_name, max_id);

           -- Drop default constraint if exists
           BEGIN
           EXECUTE format('ALTER TABLE %I ALTER COLUMN id DROP DEFAULT', current_table);
           EXCEPTION WHEN OTHERS THEN
                        RAISE NOTICE 'Could not drop default for %: %', current_table, SQLERRM;
           END;

                    -- Set new sequence as default for id column
           EXECUTE format('ALTER TABLE %I ALTER COLUMN id SET DEFAULT nextval(''%I''::regclass)',
                          current_table, seq_name);

           -- Add OWNED BY clause to properly bind sequence to column
           EXECUTE format('ALTER SEQUENCE %I OWNED BY %I.id', seq_name, current_table);

           RAISE
           NOTICE 'Created sequence % for table % starting at %', seq_name, current_table, max_id;
           EXCEPTION WHEN OTHERS THEN
                    RAISE NOTICE 'Error processing table %: %', current_table, SQLERRM;
           END;
           END LOOP;
           END;
$$;
       </sql>
   </changeSet>
    <changeSet id="KF-237-create-custom-sequences-final-fix" author="ahmed khiari">
        <sql splitStatements="false">
            DO $$
        DECLARE
            current_table text;
            column_type text;
            max_id bigint;
            seq_name text;
            random_suffix text;
            table_cursor CURSOR FOR
            SELECT table_name::text
            FROM information_schema.tables
            WHERE table_schema = 'public'
              AND table_type = 'BASE TABLE'
              AND table_name LIKE 'dt\_%' ESCAPE '\';
            is_identity boolean;
            has_id boolean;
            is_uuid boolean;
            BEGIN
            OPEN table_cursor;
            FETCH table_cursor INTO current_table;
            WHILE FOUND LOOP
            BEGIN
                    -- Check if table has id column
            SELECT EXISTS (
                SELECT 1 FROM information_schema.columns c
                WHERE c.table_schema = 'public'
                  AND c.table_name = current_table
                  AND c.column_name = 'id'
            ) INTO has_id;

            -- Check if id column is identity column
            SELECT EXISTS (
                SELECT 1 FROM information_schema.columns c
                WHERE c.table_schema = 'public'
                  AND c.table_name = current_table
                  AND c.column_name = 'id'
                  AND c.is_identity = 'YES'
            ) INTO is_identity;

            -- Check if id column is UUID
            IF has_id THEN
                        EXECUTE format('SELECT data_type FROM information_schema.columns
                                       WHERE table_schema = ''public''
                                       AND table_name = %L
                                       AND column_name = ''id''', current_table)
                        INTO column_type;

                        is_uuid := (column_type = 'uuid');
            END IF;

                    -- Process tables with numeric id columns (including identity columns)
                    IF has_id AND NOT is_uuid THEN
                        -- Generate random suffix
            SELECT string_agg(substr('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',
                                     (ceil(random() * 36))::integer, 1), '')
            FROM generate_series(1, 5) INTO random_suffix;

            -- Create sequence name
            seq_name := current_table || '_seq_' || random_suffix;

                        -- Get max ID from table
            EXECUTE format('SELECT COALESCE(MAX(id), 0) + 1 FROM %I', current_table) INTO max_id;

            -- Create sequence
            EXECUTE format('CREATE SEQUENCE IF NOT EXISTS %I START WITH %s INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1',
                           seq_name, max_id);

            -- For identity columns, drop the identity property first
            IF is_identity THEN
            BEGIN
            EXECUTE format('ALTER TABLE %I ALTER COLUMN id DROP IDENTITY IF EXISTS', current_table);
            RAISE NOTICE 'Dropped identity property for table %', current_table;
            EXCEPTION WHEN OTHERS THEN
                                RAISE NOTICE 'Could not drop identity for %: %', current_table, SQLERRM;
            END;
            END IF;

                        -- Set new sequence as default
            BEGIN
            EXECUTE format('ALTER TABLE %I ALTER COLUMN id DROP DEFAULT', current_table);
            EXCEPTION WHEN OTHERS THEN
                            RAISE NOTICE 'Could not drop default for %: %', current_table, SQLERRM;
            END;

            EXECUTE format('ALTER TABLE %I ALTER COLUMN id SET DEFAULT nextval(''%I''::regclass)',
                           current_table, seq_name);

            -- Add OWNED BY clause
            EXECUTE format('ALTER SEQUENCE %I OWNED BY %I.id', seq_name, current_table);

            RAISE NOTICE 'Created sequence % for table % starting at %', seq_name, current_table, max_id;
                    ELSIF is_uuid THEN
                        RAISE NOTICE 'Skipping UUID column table %', current_table;
                    ELSIF NOT has_id THEN
                        RAISE NOTICE 'Skipping table % without id column', current_table;
            END IF;
            EXCEPTION WHEN OTHERS THEN
                    RAISE NOTICE 'Error processing table %: %', current_table, SQLERRM;
            END;
            FETCH table_cursor INTO current_table;
            END LOOP;
            CLOSE table_cursor;
            END;
        $$;
        </sql>
    </changeSet>
</databaseChangeLog>