<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd
                                       http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="KF-240-1" author="Massoud FARDI">
        <loadUpdateData tableName="dt_static_roles"
                        primaryKey="id"
                        file="classpath:db/changelog/data/update_dt_static_roles.csv"
                        separator=",">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="is_exclusive" type="BOOLEAN"/>
            <column name="is_client" type="BOOLEAN"/>
            <column name="associated_to" type="STRING"/>
            <column name="created_at" type="DATE"/>
            <column name="modified_at" type="DATE"/>
            <column name="deleted_at" type="DATE"/>
        </loadUpdateData>
    </changeSet>

    <changeSet id="KF-240-2" author="Massoud FARDI">
        <sql>
            UPDATE dt_roles
            SET static_role_code = 'TIEPAY'
            WHERE code = 'TIEPAY';
        </sql>
    </changeSet>


</databaseChangeLog>