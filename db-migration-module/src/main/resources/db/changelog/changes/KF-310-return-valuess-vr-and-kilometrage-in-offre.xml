<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="acquisition-constraint-001" author="AhmedKhiari">
        <!-- First handle existing invalid data -->
        <sql>
            UPDATE dt_application_criteria
            SET channel_of_acquisition = 'DIRECT'
            WHERE channel_of_acquisition IS NOT NULL
              AND channel_of_acquisition NOT IN ('DIRECT', 'INDIRECT', 'PARTNER');
        </sql>

        <!-- Add check constraint to ensure only enum values are allowed -->
        <sql>
            ALTER TABLE dt_application_criteria
                ADD CONSTRAINT chk_channel_of_acquisition_enum
                    CHECK (channel_of_acquisition IN ('DIRECT', 'INDIRECT', 'PARTNER'));
        </sql>
    </changeSet>
</databaseChangeLog>