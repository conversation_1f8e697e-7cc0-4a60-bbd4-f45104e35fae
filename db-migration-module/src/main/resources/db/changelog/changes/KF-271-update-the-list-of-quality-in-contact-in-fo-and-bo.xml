<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">


    <changeSet id="KF-271-001" author="Massoud FARDI">
        <createTable tableName="dt_positions">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="language" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="KF-271-002" author="Massoud FARDI">
        <loadData file="classpath:db/changelog/data/dt_positions.csv"
                  tableName="dt_positions"
                  separator=","
                  encoding="UTF-8">
            <column name="id" type="BIGINT"/>
            <column name="code" type="VARCHAR(255)"/>
            <column name="label" type="VARCHAR(255)"/>
            <column name="language" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)"/>
        </loadData>
    </changeSet>

    <changeSet id="KF-271-003" author="Massoud FARDI">

        <addColumn tableName="dt_contacts">
            <column name="quality_code" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <sql>
            UPDATE dt_contacts c
            SET quality_code = (
                SELECT p.code
                FROM dt_positions p
                WHERE p.name = c.quality AND p.language = 'fr'
                LIMIT 1
                )
            WHERE c.quality IS NOT NULL;
        </sql>

        <addForeignKeyConstraint baseTableName="dt_contacts"
                                 baseColumnNames="quality_code"
                                 referencedTableName="dt_positions"
                                 referencedColumnNames="code"
                                 constraintName="fk_dt_contacts_quality_code"/>

        <dropColumn tableName="dt_contacts" columnName="quality"/>
    </changeSet>

    <changeSet id="KF-271-004" author="Massoud FARDI">
        <addColumn tableName="dt_positions">
            <column name="created_at" type="TIMESTAMP"/>
            <column name="modified_at" type="TIMESTAMP"/>
            <column name="deleted_at" type="TIMESTAMP"/>
        </addColumn>

        <update tableName="dt_positions">
            <column name="created_at" valueDate="2025-05-27 13:35:05.913679"/>
            <where>deleted_at IS NULL</where>
        </update>
    </changeSet>

    <changeSet id="KF-271-005" author="Massoud FARDI">

        <dropForeignKeyConstraint baseTableName="dt_contacts" constraintName="fk_dt_contacts_quality_code"/>

        <sql>
            UPDATE dt_contacts
            SET quality_code = 'ADMINFR'
            WHERE quality_code IS NOT NULL;
        </sql>

        <delete tableName="dt_positions">
            <where>deleted_at IS NULL</where>
        </delete>

        <loadData file="classpath:db/changelog/data/update_dt_positions.csv"
                  tableName="dt_positions"
                  separator=","
                  encoding="UTF-8">
            <column name="id" type="BIGINT"/>
            <column name="code" type="VARCHAR(255)"/>
            <column name="label" type="VARCHAR(255)"/>
            <column name="language" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="created_at" type="TIMESTAMP"/>
            <column name="modified_at" type="TIMESTAMP"/>
            <column name="deleted_at" type="TIMESTAMP"/>
        </loadData>

        <addForeignKeyConstraint baseTableName="dt_contacts"
                                 baseColumnNames="quality_code"
                                 referencedTableName="dt_positions"
                                 referencedColumnNames="code"
                                 constraintName="fk_dt_contacts_quality_code"/>
    </changeSet>


    <changeSet id="KF-271-006" author="Massoud FARDI">

        <sql>
            UPDATE dt_simulation_actor_payments
            SET type = 'Decashment'
            WHERE type = 'Disbursement';
        </sql>

        <sql>
            UPDATE dt_simulation_actor_payments
            SET type = 'Encashment'
            WHERE type = 'Collection';
        </sql>
    </changeSet>

</databaseChangeLog>