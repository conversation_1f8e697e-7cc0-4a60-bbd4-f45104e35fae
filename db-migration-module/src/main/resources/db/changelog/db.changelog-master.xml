<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <property name="table.prefix" value="dt_"/>
    <include file="db/changelog/changes/liquibase-outputChangeLog-27-03-2025.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-72-add-static-tables.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-72-add-static-tables-part-2.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/Add-Activity-products.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-96-bug-collection-for-actor-module.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-100-add-static-table-for-vehicles-characteristics.xml"
             relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-97-simulation-create-and-update-getsimulation-autres-equipements.xml"
             relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-105-bugs.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-102-fix-both-settlement-means-and-facturation-for-role.xml"
             relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-56.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-131-add-synchronisation-part-for-naf-legal-category-and-market.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-130-backend-implement-the-recap-part.xml"
             relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-141.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/add-unique-constraint-to-scale-related-tables.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-144-update-simulation-actor-file.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-163-fix-nap-sync.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-174-fix-tax.relation.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-175-backend-management-of-services.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-176-references.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-189-remove-market-from-vehicule-and-add-services-to-scale.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-122-manage-rentals.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-202-fix-problem-related-to-scale.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-207.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-237-hotfix-test-fix-backend-problems.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-240-hotfix-add-tier-payeur-to-liquibase.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-261-fix-bank-account-branch.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-271-update-the-list-of-quality-in-contact-in-fo-and-bo.xml" relativeToChangelogFile="false"/>
    <include file="db/changelog/changes/KF-310-return-valuess-vr-and-kilometrage-in-offre.xml" relativeToChangelogFile="false"/>
</databaseChangeLog>
